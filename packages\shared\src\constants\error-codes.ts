/**
 * HER 错误代码常量
 * 统一管理所有错误类型和消息
 */

// 认证错误
export const AUTH_ERRORS = {
  INVALID_TOKEN: 'AUTH_INVALID_TOKEN',
  TOKEN_EXPIRED: 'AUTH_TOKEN_EXPIRED',
  UNAUTHORIZED: 'AUTH_UNAUTHORIZED',
  PHONE_INVALID: 'AUTH_PHONE_INVALID',
  CODE_INVALID: 'AUTH_CODE_INVALID',
  CODE_EXPIRED: 'AUTH_CODE_EXPIRED',
  USER_NOT_FOUND: 'AUTH_USER_NOT_FOUND',
  SESSION_EXPIRED: 'AUTH_SESSION_EXPIRED',
  LOGIN_FAILED: 'AUTH_LOGIN_FAILED',
  LOGOUT_FAILED: 'AUTH_LOGOUT_FAILED',
} as const;

// 用户错误
export const USER_ERRORS = {
  NOT_FOUND: 'USER_NOT_FOUND',
  ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  UPDATE_FAILED: 'USER_UPDATE_FAILED',
  PREFERENCES_INVALID: 'USER_PREFERENCES_INVALID',
  DELETION_FAILED: 'USER_DELETION_FAILED',
} as const;

// 对话错误
export const CHAT_ERRORS = {
  MESSAGE_TOO_LONG: 'CHAT_MESSAGE_TOO_LONG',
  MESSAGE_EMPTY: 'CHAT_MESSAGE_EMPTY',
  CONVERSATION_NOT_FOUND: 'CHAT_CONVERSATION_NOT_FOUND',
  SEND_FAILED: 'CHAT_SEND_FAILED',
  STREAM_ERROR: 'CHAT_STREAM_ERROR',
  ENCRYPTION_FAILED: 'CHAT_ENCRYPTION_FAILED',
  DECRYPTION_FAILED: 'CHAT_DECRYPTION_FAILED',
  RATE_LIMITED: 'CHAT_RATE_LIMITED',
} as const;

// 记忆系统错误
export const MEMORY_ERRORS = {
  SAVE_FAILED: 'MEMORY_SAVE_FAILED',
  SEARCH_FAILED: 'MEMORY_SEARCH_FAILED',
  NOT_FOUND: 'MEMORY_NOT_FOUND',
  EMBEDDING_FAILED: 'MEMORY_EMBEDDING_FAILED',
  VECTOR_SEARCH_FAILED: 'MEMORY_VECTOR_SEARCH_FAILED',
  CLEANUP_FAILED: 'MEMORY_CLEANUP_FAILED',
  QUOTA_EXCEEDED: 'MEMORY_QUOTA_EXCEEDED',
} as const;

// 情绪系统错误
export const MOOD_ERRORS = {
  INVALID_VALUE: 'MOOD_INVALID_VALUE',
  SAVE_FAILED: 'MOOD_SAVE_FAILED',
  NOT_FOUND: 'MOOD_NOT_FOUND',
  HISTORY_FAILED: 'MOOD_HISTORY_FAILED',
} as const;

// 网络错误
export const NETWORK_ERRORS = {
  CONNECTION_FAILED: 'NETWORK_CONNECTION_FAILED',
  TIMEOUT: 'NETWORK_TIMEOUT',
  OFFLINE: 'NETWORK_OFFLINE',
  SERVER_ERROR: 'NETWORK_SERVER_ERROR',
  BAD_REQUEST: 'NETWORK_BAD_REQUEST',
  NOT_FOUND: 'NETWORK_NOT_FOUND',
  FORBIDDEN: 'NETWORK_FORBIDDEN',
} as const;

// 系统错误
export const SYSTEM_ERRORS = {
  INTERNAL_ERROR: 'SYSTEM_INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SYSTEM_SERVICE_UNAVAILABLE',
  MAINTENANCE: 'SYSTEM_MAINTENANCE',
  VERSION_MISMATCH: 'SYSTEM_VERSION_MISMATCH',
  CONFIG_ERROR: 'SYSTEM_CONFIG_ERROR',
} as const;

// 存储错误
export const STORAGE_ERRORS = {
  READ_FAILED: 'STORAGE_READ_FAILED',
  WRITE_FAILED: 'STORAGE_WRITE_FAILED',
  QUOTA_EXCEEDED: 'STORAGE_QUOTA_EXCEEDED',
  CORRUPTION: 'STORAGE_CORRUPTION',
  PERMISSION_DENIED: 'STORAGE_PERMISSION_DENIED',
} as const;

// 上传错误
export const UPLOAD_ERRORS = {
  FILE_TOO_LARGE: 'UPLOAD_FILE_TOO_LARGE',
  INVALID_FORMAT: 'UPLOAD_INVALID_FORMAT',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  PROCESSING_FAILED: 'UPLOAD_PROCESSING_FAILED',
} as const;

// 所有错误代码的联合类型
export type ErrorCode = 
  | (typeof AUTH_ERRORS)[keyof typeof AUTH_ERRORS]
  | (typeof USER_ERRORS)[keyof typeof USER_ERRORS]
  | (typeof CHAT_ERRORS)[keyof typeof CHAT_ERRORS]
  | (typeof MEMORY_ERRORS)[keyof typeof MEMORY_ERRORS]
  | (typeof MOOD_ERRORS)[keyof typeof MOOD_ERRORS]
  | (typeof NETWORK_ERRORS)[keyof typeof NETWORK_ERRORS]
  | (typeof SYSTEM_ERRORS)[keyof typeof SYSTEM_ERRORS]
  | (typeof STORAGE_ERRORS)[keyof typeof STORAGE_ERRORS]
  | (typeof UPLOAD_ERRORS)[keyof typeof UPLOAD_ERRORS];

// 错误消息映射
export const ERROR_MESSAGES: Record<ErrorCode, string> = {
  // 认证错误消息
  [AUTH_ERRORS.INVALID_TOKEN]: '无效的访问令牌',
  [AUTH_ERRORS.TOKEN_EXPIRED]: '访问令牌已过期，请重新登录',
  [AUTH_ERRORS.UNAUTHORIZED]: '未授权访问',
  [AUTH_ERRORS.PHONE_INVALID]: '手机号格式不正确',
  [AUTH_ERRORS.CODE_INVALID]: '验证码错误',
  [AUTH_ERRORS.CODE_EXPIRED]: '验证码已过期',
  [AUTH_ERRORS.USER_NOT_FOUND]: '用户不存在',
  [AUTH_ERRORS.SESSION_EXPIRED]: '会话已过期，请重新登录',
  [AUTH_ERRORS.LOGIN_FAILED]: '登录失败',
  [AUTH_ERRORS.LOGOUT_FAILED]: '退出登录失败',

  // 用户错误消息
  [USER_ERRORS.NOT_FOUND]: '用户不存在',
  [USER_ERRORS.ALREADY_EXISTS]: '用户已存在',
  [USER_ERRORS.UPDATE_FAILED]: '更新用户信息失败',
  [USER_ERRORS.PREFERENCES_INVALID]: '用户偏好设置无效',
  [USER_ERRORS.DELETION_FAILED]: '删除用户失败',

  // 对话错误消息
  [CHAT_ERRORS.MESSAGE_TOO_LONG]: '消息内容过长',
  [CHAT_ERRORS.MESSAGE_EMPTY]: '消息内容不能为空',
  [CHAT_ERRORS.CONVERSATION_NOT_FOUND]: '对话不存在',
  [CHAT_ERRORS.SEND_FAILED]: '发送消息失败',
  [CHAT_ERRORS.STREAM_ERROR]: '实时对话连接出现问题',
  [CHAT_ERRORS.ENCRYPTION_FAILED]: '消息加密失败',
  [CHAT_ERRORS.DECRYPTION_FAILED]: '消息解密失败',
  [CHAT_ERRORS.RATE_LIMITED]: '发送过于频繁，请稍后再试',

  // 记忆系统错误消息
  [MEMORY_ERRORS.SAVE_FAILED]: '保存记忆失败',
  [MEMORY_ERRORS.SEARCH_FAILED]: '搜索记忆失败',
  [MEMORY_ERRORS.NOT_FOUND]: '记忆不存在',
  [MEMORY_ERRORS.EMBEDDING_FAILED]: '生成记忆向量失败',
  [MEMORY_ERRORS.VECTOR_SEARCH_FAILED]: '向量搜索失败',
  [MEMORY_ERRORS.CLEANUP_FAILED]: '清理记忆失败',
  [MEMORY_ERRORS.QUOTA_EXCEEDED]: '记忆存储空间已满',

  // 情绪系统错误消息
  [MOOD_ERRORS.INVALID_VALUE]: '情绪值无效',
  [MOOD_ERRORS.SAVE_FAILED]: '保存情绪记录失败',
  [MOOD_ERRORS.NOT_FOUND]: '情绪记录不存在',
  [MOOD_ERRORS.HISTORY_FAILED]: '获取情绪历史失败',

  // 网络错误消息
  [NETWORK_ERRORS.CONNECTION_FAILED]: '网络连接失败',
  [NETWORK_ERRORS.TIMEOUT]: '请求超时',
  [NETWORK_ERRORS.OFFLINE]: '网络已断开',
  [NETWORK_ERRORS.SERVER_ERROR]: '服务器错误',
  [NETWORK_ERRORS.BAD_REQUEST]: '请求参数错误',
  [NETWORK_ERRORS.NOT_FOUND]: '请求的资源不存在',
  [NETWORK_ERRORS.FORBIDDEN]: '访问被禁止',

  // 系统错误消息
  [SYSTEM_ERRORS.INTERNAL_ERROR]: '系统内部错误',
  [SYSTEM_ERRORS.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  [SYSTEM_ERRORS.MAINTENANCE]: '系统正在维护中',
  [SYSTEM_ERRORS.VERSION_MISMATCH]: '应用版本不匹配，请更新',
  [SYSTEM_ERRORS.CONFIG_ERROR]: '系统配置错误',

  // 存储错误消息
  [STORAGE_ERRORS.READ_FAILED]: '读取数据失败',
  [STORAGE_ERRORS.WRITE_FAILED]: '保存数据失败',
  [STORAGE_ERRORS.QUOTA_EXCEEDED]: '存储空间不足',
  [STORAGE_ERRORS.CORRUPTION]: '数据已损坏',
  [STORAGE_ERRORS.PERMISSION_DENIED]: '存储权限被拒绝',

  // 上传错误消息
  [UPLOAD_ERRORS.FILE_TOO_LARGE]: '文件太大',
  [UPLOAD_ERRORS.INVALID_FORMAT]: '文件格式不支持',
  [UPLOAD_ERRORS.UPLOAD_FAILED]: '上传失败',
  [UPLOAD_ERRORS.PROCESSING_FAILED]: '文件处理失败',
} as const;

// 获取错误消息的辅助函数
export const getErrorMessage = (code: ErrorCode): string => {
  return ERROR_MESSAGES[code] || '未知错误';
};