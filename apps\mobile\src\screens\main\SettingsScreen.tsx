/**
 * HER Settings Screen
 * 设置页面
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
} from 'react-native';
import { useTheme } from '@her/ui';
import { useAuthStore } from '../../stores/authStore';
import { useAppStore } from '../../stores/appStore';

export const SettingsScreen: React.FC = () => {
  const theme = useTheme();
  const { user, logout } = useAuthStore();
  const { 
    theme: appTheme, 
    settings, 
    toggleThemeMode, 
    updateSettings, 
    enableAutoTimeTheme 
  } = useAppStore();

  const handleLogout = () => {
    Alert.alert(
      '确认退出',
      '退出后需要重新登录才能继续使用',
      [
        { text: '取消', style: 'cancel' },
        { 
          text: '确定', 
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Logout failed:', error);
            }
          }
        },
      ]
    );
  };

  const SettingItem: React.FC<{
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightComponent?: React.ReactNode;
    showArrow?: boolean;
  }> = ({ title, subtitle, onPress, rightComponent, showArrow = false }) => (
    <TouchableOpacity
      style={[styles.settingItem, { backgroundColor: theme.colors.surface }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, { color: theme.colors.text.primary }]}>
          {title}
        </Text>
        {subtitle && (
          <Text style={[styles.settingSubtitle, { color: theme.colors.text.secondary }]}>
            {subtitle}
          </Text>
        )}
      </View>
      {rightComponent}
      {showArrow && (
        <Text style={[styles.arrow, { color: theme.colors.text.secondary }]}>
          →
        </Text>
      )}
    </TouchableOpacity>
  );

  const SettingSection: React.FC<{
    title: string;
    children: React.ReactNode;
  }> = ({ title, children }) => (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: theme.colors.text.secondary }]}>
        {title}
      </Text>
      <View style={styles.sectionContent}>
        {children}
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          设置
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Section */}
        <SettingSection title="用户信息">
          <SettingItem
            title={user?.nickname || '匿名用户'}
            subtitle={user?.phone ? `手机号: ${user.phone}` : '匿名登录用户'}
            showArrow
            onPress={() => {
              Alert.alert('功能开发中', '个人资料编辑功能即将上线');
            }}
          />
        </SettingSection>

        {/* Appearance Section */}
        <SettingSection title="外观设置">
          <SettingItem
            title="主题模式"
            subtitle={
              appTheme.mode === 'light' ? '浅色模式' : 
              appTheme.mode === 'dark' ? '深色模式' : '跟随系统'
            }
            onPress={toggleThemeMode}
            showArrow
          />
          
          <SettingItem
            title="时间自动主题"
            subtitle="根据时间自动调整主题风格"
            rightComponent={
              <Switch
                value={appTheme.isAutoTime}
                onValueChange={enableAutoTimeTheme}
                trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
                thumbColor={theme.colors.surface}
              />
            }
          />
        </SettingSection>

        {/* Notification Section */}
        <SettingSection title="通知设置">
          <SettingItem
            title="推送通知"
            subtitle="接收消息和提醒通知"
            rightComponent={
              <Switch
                value={settings.notificationsEnabled}
                onValueChange={(value) => updateSettings({ notificationsEnabled: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
                thumbColor={theme.colors.surface}
              />
            }
          />
          
          <SettingItem
            title="声音提醒"
            subtitle="播放通知提示音"
            rightComponent={
              <Switch
                value={settings.soundEnabled}
                onValueChange={(value) => updateSettings({ soundEnabled: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
                thumbColor={theme.colors.surface}
              />
            }
          />
          
          <SettingItem
            title="震动反馈"
            subtitle="操作时的触觉反馈"
            rightComponent={
              <Switch
                value={settings.hapticsEnabled}
                onValueChange={(value) => updateSettings({ hapticsEnabled: value })}
                trackColor={{ false: theme.colors.border, true: theme.colors.accent }}
                thumbColor={theme.colors.surface}
              />
            }
          />
        </SettingSection>

        {/* Privacy Section */}
        <SettingSection title="隐私与安全">
          <SettingItem
            title="数据与隐私"
            subtitle="管理您的数据和隐私设置"
            showArrow
            onPress={() => {
              Alert.alert('功能开发中', '隐私设置功能即将上线');
            }}
          />
          
          <SettingItem
            title="清除聊天记录"
            subtitle="删除所有对话历史"
            onPress={() => {
              Alert.alert(
                '确认清除',
                '此操作将删除所有聊天记录，且无法恢复',
                [
                  { text: '取消', style: 'cancel' },
                  { text: '清除', style: 'destructive' }
                ]
              );
            }}
            showArrow
          />
        </SettingSection>

        {/* About Section */}
        <SettingSection title="关于">
          <SettingItem
            title="服务条款"
            showArrow
            onPress={() => {
              Alert.alert('功能开发中', '服务条款功能即将上线');
            }}
          />
          
          <SettingItem
            title="隐私政策"
            showArrow
            onPress={() => {
              Alert.alert('功能开发中', '隐私政策功能即将上线');
            }}
          />
          
          <SettingItem
            title="版本信息"
            subtitle="v1.0.0 (Build 1)"
          />
        </SettingSection>

        {/* Logout */}
        <View style={styles.logoutSection}>
          <TouchableOpacity
            style={[styles.logoutButton, { borderColor: '#E8A5A5' }]}
            onPress={handleLogout}
          >
            <Text style={styles.logoutText}>退出登录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60, // 状态栏高度
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    paddingHorizontal: 24,
    marginBottom: 12,
  },
  sectionContent: {
    paddingHorizontal: 24,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    lineHeight: 18,
  },
  arrow: {
    fontSize: 16,
    marginLeft: 8,
  },
  logoutSection: {
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  logoutButton: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E8A5A5',
  },
});