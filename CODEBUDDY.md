
This file provides guidance to CodeBuddy Code when working with code in this repository.

## Project Overview

HER is a private AI companion app built with React Native, Vercel, and Supabase. It uses a `pnpm` workspaces monorepo structure and is written entirely in TypeScript. The core focus is on privacy, emotional intelligence, and performance.

## Architecture

The project is a monorepo with the following structure:
- `apps/mobile/`: The main React Native (Expo) mobile application.
- `packages/api/`: The backend, implemented as Vercel Edge Functions.
- `packages/shared/`: Shared code, including TypeScript types, utility functions, and constants.
- `packages/ui/`: A shared library of React Native components.

### Tech Stack
- **Frontend**: React Native (Expo), Zustand for state management, React Navigation for routing.
- **Backend**: Vercel Edge Functions, Supabase (PostgreSQL with pgvector, Auth, Storage), OpenAI API for AI features, and Vercel KV for caching.
- **Tooling**: TypeScript, pnpm, Jest, React Native Testing Library, ESLint, Prettier.

### Development Workflow Dependencies
- `apps/mobile` depends on `packages/shared` and `packages/ui`.
- `packages/api` depends on `packages/shared`.
- The shared packages (`shared`, `ui`) must be built (`pnpm <package>:build`) before the applications that depend on them can be developed or built. A `postinstall` script handles the initial build.

## Common Commands

### Setup
- `pnpm install`: Installs all dependencies and builds the shared packages.
- `pnpm reset`: Cleans the project, removes `node_modules`, and reinstalls everything.

### Development
- `pnpm dev`: Starts all development services concurrently (mobile and API).
- `pnpm mobile:ios`: Runs the mobile app on the iOS simulator.
- `pnpm mobile:android`: Runs the mobile app on the Android emulator.
- `pnpm api:dev`: Starts the Vercel development server for the API.

### Building
- `pnpm build`: Builds all packages in the monorepo.
- `pnpm shared:build`: Builds the `shared` package.
- `pnpm ui:build`: Builds the `ui` package.

### Testing and Quality
- `pnpm test`: Runs all tests across the project.
- `pnpm --filter <name> test`: Runs tests for a specific package (e.g., `pnpm --filter mobile test`).
- `pnpm lint`: Lints the entire codebase.
- `pnpm typecheck`: Runs the TypeScript compiler to check for type errors.
- `pnpm format`: Formats code using Prettier.
- **Pre-commit hooks** are set up to run `lint`, `test`, and `typecheck` before committing.
