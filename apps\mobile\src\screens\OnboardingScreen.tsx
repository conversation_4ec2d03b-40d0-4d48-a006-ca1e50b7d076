/**
 * HER Onboarding Screen
 * 应用引导页面
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import { useTheme } from '@her/ui';
import { useAppStore } from '../stores/appStore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '@her/shared';

const { width: screenWidth } = Dimensions.get('window');

interface OnboardingStep {
  title: string;
  description: string;
  emoji: string;
}

const onboardingSteps: OnboardingStep[] = [
  {
    title: '欢迎来到 HER',
    description: '一个温暖、私密的AI陪伴应用，随时倾听你的心声。',
    emoji: '🌸',
  },
  {
    title: '私密安全',
    description: '你的对话完全私密，采用端到端加密，只属于你和她。',
    emoji: '🔒',
  },
  {
    title: '情感理解',
    description: '她能感知你的情绪变化，给予最贴心的陪伴与支持。',
    emoji: '💝',
  },
  {
    title: '记忆延续',
    description: '她会记住你们的对话，让每一次交流都有温度和延续性。',
    emoji: '🧠',
  },
];

export const OnboardingScreen: React.FC = () => {
  const theme = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const { initializeApp } = useAppStore();

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleSkip = () => {
    handleComplete();
  };

  const handleComplete = async () => {
    try {
      // 标记引导完成
      await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
      
      // 重新初始化应用状态
      await initializeApp();
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
    }
  };

  const currentStepData = onboardingSteps[currentStep];

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.stepIndicator, { color: theme.colors.text.secondary }]}>
          {currentStep + 1} / {onboardingSteps.length}
        </Text>
        
        {currentStep < onboardingSteps.length - 1 && (
          <TouchableOpacity onPress={handleSkip}>
            <Text style={[styles.skipButton, { color: theme.colors.text.secondary }]}>
              跳过
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Progress Bar */}
      <View style={[styles.progressBar, { backgroundColor: theme.colors.border }]}>
        <View
          style={[
            styles.progressFill,
            {
              backgroundColor: theme.colors.accent,
              width: `${((currentStep + 1) / onboardingSteps.length) * 100}%`,
            },
          ]}
        />
      </View>

      {/* Content */}
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.stepContent}>
          <Text style={styles.emoji}>{currentStepData.emoji}</Text>
          
          <Text style={[styles.title, { color: theme.colors.text.primary }]}>
            {currentStepData.title}
          </Text>
          
          <Text style={[styles.description, { color: theme.colors.text.secondary }]}>
            {currentStepData.description}
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.nextButton,
            { backgroundColor: theme.colors.accent },
          ]}
          onPress={handleNext}
        >
          <Text style={[styles.nextButtonText, { color: theme.colors.text.primary }]}>
            {currentStep < onboardingSteps.length - 1 ? '下一步' : '开始使用'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // 状态栏高度
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  stepIndicator: {
    fontSize: 14,
    fontWeight: '500',
  },
  skipButton: {
    fontSize: 14,
    fontWeight: '500',
  },
  progressBar: {
    height: 4,
    marginHorizontal: 24,
    borderRadius: 2,
    marginBottom: 48,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  stepContent: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emoji: {
    fontSize: 80,
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    paddingHorizontal: 24,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 34, // 安全区域
  },
  nextButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});