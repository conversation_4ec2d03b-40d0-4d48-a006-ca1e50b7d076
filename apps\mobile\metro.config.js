/**
 * Metro configuration for React Native
 */

const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// 获取项目根目录
const projectRoot = __dirname;
const workspaceRoot = path.resolve(projectRoot, '../..');

const config = getDefaultConfig(projectRoot);

// 1. 监视根目录和packages
config.watchFolders = [workspaceRoot];

// 2. 让Metro能找到monorepo中的包
config.resolver.disableHierarchicalLookup = true;
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(workspaceRoot, 'node_modules'),
];

// 3. 处理共享包的符号链接
config.resolver.unstable_enableSymlinks = true;

// 4. 添加对TypeScript和其他文件类型的支持
config.resolver.sourceExts.push('ts', 'tsx', 'js', 'jsx', 'json');

// 5. 处理包的入口点
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

module.exports = config;