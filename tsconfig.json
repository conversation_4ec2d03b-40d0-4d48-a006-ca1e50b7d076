{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@her/*": ["./packages/shared/src/*"], "@her/ui": ["./packages/ui/src"], "@her/types": ["./packages/shared/src/types"]}, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "dist", "build", "**/node_modules", "**/dist", "**/build"], "references": [{"path": "./packages/shared"}, {"path": "./packages/ui"}, {"path": "./packages/api"}, {"path": "./apps/mobile"}]}