/**
 * HER Authentication Store
 * 用户认证状态管理
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, STORAGE_KEYS } from '@her/shared';
import { authService } from '../services/authService';

interface AuthState {
  // 认证状态
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
  
  // 认证方法
  loginAnonymously: () => Promise<void>;
  loginWithPhone: (phone: string, code: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateUser: (updates: Partial<User>) => Promise<void>;
  
  // 内部方法
  setUser: (user: User | null, token?: string) => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: false,
      isLoading: true, // 初始时为loading状态
      user: null,
      token: null,
      
      // 匿名登录
      loginAnonymously: async () => {
        try {
          set({ isLoading: true });
          
          const response = await authService.loginAnonymously();
          
          set({
            isAuthenticated: true,
            user: response.user,
            token: response.token,
            isLoading: false,
          });
        } catch (error) {
          console.error('Anonymous login failed:', error);
          set({ isLoading: false });
          throw error;
        }
      },
      
      // 手机号登录
      loginWithPhone: async (phone: string, code: string) => {
        try {
          set({ isLoading: true });
          
          const response = await authService.loginWithPhone({ phone, code });
          
          set({
            isAuthenticated: true,
            user: response.user,
            token: response.token,
            isLoading: false,
          });
        } catch (error) {
          console.error('Phone login failed:', error);
          set({ isLoading: false });
          throw error;
        }
      },
      
      // 退出登录
      logout: async () => {
        try {
          const { token } = get();
          if (token) {
            await authService.logout();
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            isAuthenticated: false,
            user: null,
            token: null,
            isLoading: false,
          });
          
          // 清理存储
          await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        }
      },
      
      // 刷新令牌
      refreshToken: async () => {
        try {
          const { token } = get();
          if (!token) return;
          
          const response = await authService.refreshToken();
          
          set({
            token: response.token,
            user: response.user,
          });
        } catch (error) {
          console.error('Token refresh failed:', error);
          // 如果刷新失败，退出登录
          await get().logout();
          throw error;
        }
      },
      
      // 更新用户信息
      updateUser: async (updates: Partial<User>) => {
        try {
          const { user } = get();
          if (!user) return;
          
          const updatedUser = await authService.updateProfile(updates);
          
          set({
            user: updatedUser,
          });
        } catch (error) {
          console.error('Update user failed:', error);
          throw error;
        }
      },
      
      // 设置用户和令牌
      setUser: (user: User | null, token?: string) => {
        set({
          user,
          token: token || get().token,
          isAuthenticated: !!user,
        });
      },
      
      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'her-auth-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // 只持久化这些字段
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // 重新加载后设置loading状态为false
        if (state) {
          state.isLoading = false;
        }
      },
    }
  )
);