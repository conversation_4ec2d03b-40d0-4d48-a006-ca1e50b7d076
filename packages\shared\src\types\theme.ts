/**
 * HER 主题系统类型定义
 * 基于现有的设计令牌系统
 */

// 颜色调色板
export interface ColorPalette {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: TextColors;
  bubble: BubbleColors;
  accent: string;
  border: string;
  shadow: string;
}

export interface TextColors {
  primary: string;
  secondary: string;
  disabled: string;
}

export interface BubbleColors {
  her: string;
  user: string;
}

// 情绪化颜色
export interface EmotionalColors {
  listening: EmotionalColorSet;
  thinking: EmotionalColorSet;
  caring: EmotionalColorSet;
  understanding: EmotionalColorSet;
}

export interface EmotionalColorSet {
  primary: string;
  accent: string;
  bubble: string;
}

// 字体定义
export interface Typography {
  fontFamilies: {
    chinese: string;
    english: string;
    chat: string;
  };
  fontSizes: {
    largeTitle: number;
    title1: number;
    title2: number;
    title3: number;
    large: number;
    body: number;
    callout: number;
    subhead: number;
    footnote: number;
    caption1: number;
    caption2: number;
  };
  lineHeights: {
    tight: number;
    normal: number;
    relaxed: number;
    loose: number;
  };
  fontWeights: {
    light: number;
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
  };
}

// 间距系统
export interface Spacing {
  scale: {
    xxs: number;
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
    xxxl: number;
  };
  chat: {
    betweenMessages: number;
    bubblePaddingH: number;
    bubblePaddingV: number;
    inputPadding: number;
  };
}

// 圆角系统
export interface BorderRadius {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  pill: number;
  bubble: number;
  button: number;
  card: number;
  input: number;
}

// 阴影系统
export interface Shadows {
  xs: ShadowConfig;
  sm: ShadowConfig;
  md: ShadowConfig;
  lg: ShadowConfig;
  bubble: ShadowConfig;
}

export interface ShadowConfig {
  color: string;
  offsetX: number;
  offsetY: number;
  blur: number;
  spread: number;
}

// 动画配置
export interface Animation {
  duration: {
    instant: number;
    fast: number;
    normal: number;
    slow: number;
    verySlow: number;
    breathing: number;
  };
  easing: {
    linear: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    spring: string;
  };
}

// 主题模式
export type ThemeMode = 'light' | 'dark' | 'auto';

// 主题变体
export type ThemeVariant = 'default' | 'peaceful' | 'warm' | 'night';

// 主题上下文
export interface ThemeContext {
  colors: ColorPalette;
  typography: Typography;
  spacing: Spacing;
  borderRadius: BorderRadius;
  shadows: Shadows;
  animation: Animation;
  mode: ThemeMode;
  variant: ThemeVariant;
  isDark: boolean;
}

// 主题配置
export interface ThemeConfig {
  name: string;
  mode: ThemeMode;
  colors: Partial<ColorPalette>;
  autoSwitchTime?: {
    morning: number; // 小时
    afternoon: number;
    evening: number;
    night: number;
  };
}