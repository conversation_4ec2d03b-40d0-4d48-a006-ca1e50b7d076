/**
 * HER 加密工具函数
 * 提供消息和敏感数据的加密/解密功能
 */

// 注意：这是一个简化的加密实现示例
// 在生产环境中，应该使用更安全的加密库如 @noble/ciphers 或 WebCrypto API

import { SECURITY_CONFIG } from '../constants/app-config';

// Base64 编码/解码辅助函数
const base64Encode = (data: string): string => {
  if (typeof Buffer !== 'undefined') {
    // Node.js 环境
    return Buffer.from(data, 'utf-8').toString('base64');
  } else {
    // 浏览器环境
    return btoa(unescape(encodeURIComponent(data)));
  }
};

const base64Decode = (encoded: string): string => {
  if (typeof Buffer !== 'undefined') {
    // Node.js 环境
    return Buffer.from(encoded, 'base64').toString('utf-8');
  } else {
    // 浏览器环境
    return decodeURIComponent(escape(atob(encoded)));
  }
};

// 简单的XOR加密（仅用于演示，生产环境需要使用更安全的方法）
const xorEncrypt = (text: string, key: string): string => {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ key.charCodeAt(i % key.length);
    result += String.fromCharCode(charCode);
  }
  return result;
};

// 生成随机密钥
export const generateKey = (length = SECURITY_CONFIG.ENCRYPTION_KEY_LENGTH): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 加密文本
export const encrypt = async (text: string, key?: string): Promise<string> => {
  try {
    if (!text) return '';
    
    const encryptionKey = key || getStoredKey();
    if (!encryptionKey) {
      throw new Error('加密密钥未找到');
    }
    
    // 添加时间戳和随机盐
    const timestamp = Date.now().toString();
    const salt = Math.random().toString(36).substring(2);
    const plaintext = `${timestamp}|${salt}|${text}`;
    
    // 使用XOR加密（生产环境应使用AES等标准算法）
    const encrypted = xorEncrypt(plaintext, encryptionKey);
    
    // Base64编码
    return base64Encode(encrypted);
  } catch (error) {
    console.error('加密失败:', error);
    throw new Error('加密失败');
  }
};

// 解密文本
export const decrypt = async (encryptedText: string, key?: string): Promise<string> => {
  try {
    if (!encryptedText) return '';
    
    const encryptionKey = key || getStoredKey();
    if (!encryptionKey) {
      throw new Error('解密密钥未找到');
    }
    
    // Base64解码
    const encrypted = base64Decode(encryptedText);
    
    // 使用XOR解密
    const decrypted = xorEncrypt(encrypted, encryptionKey);
    
    // 提取原始文本（移除时间戳和盐）
    const parts = decrypted.split('|');
    if (parts.length >= 3) {
      return parts.slice(2).join('|');
    }
    
    return decrypted;
  } catch (error) {
    console.error('解密失败:', error);
    throw new Error('解密失败');
  }
};

// 获取存储的密钥（这里简化处理，实际应该安全存储）
const getStoredKey = (): string => {
  // 这里应该从安全存储中获取密钥
  // 例如：Keychain (iOS) / Keystore (Android) / Secure Storage
  return 'default_encryption_key_2024'; // 示例密钥
};

// 哈希函数（用于密码等）
export const hash = async (text: string): Promise<string> => {
  try {
    if (typeof crypto !== 'undefined' && crypto.subtle) {
      // 使用 Web Crypto API
      const encoder = new TextEncoder();
      const data = encoder.encode(text);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } else {
      // 简单的哈希实现（仅用于演示）
      let hash = 0;
      for (let i = 0; i < text.length; i++) {
        const char = text.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
      }
      return Math.abs(hash).toString(16);
    }
  } catch (error) {
    console.error('哈希计算失败:', error);
    throw new Error('哈希计算失败');
  }
};

// 验证哈希
export const verifyHash = async (text: string, hashedText: string): Promise<boolean> => {
  try {
    const computedHash = await hash(text);
    return computedHash === hashedText;
  } catch (error) {
    console.error('哈希验证失败:', error);
    return false;
  }
};

// 生成消息摘要（用于消息去重等）
export const generateMessageDigest = async (content: string): Promise<string> => {
  try {
    // 标准化内容（移除多余空白等）
    const normalized = content.trim().replace(/\s+/g, ' ');
    return await hash(normalized);
  } catch (error) {
    console.error('生成消息摘要失败:', error);
    throw new Error('生成消息摘要失败');
  }
};

// 安全的随机字符串生成
export const generateSecureRandom = (length = 16): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    // 使用加密安全的随机数生成器
    const randomValues = new Uint8Array(length);
    crypto.getRandomValues(randomValues);
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(randomValues[i] % chars.length);
    }
  } else {
    // 回退到 Math.random()（不够安全，仅用于开发）
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
  }
  
  return result;
};

// 掩码敏感信息（用于日志等）
export const maskSensitiveData = (data: string, visibleChars = 4): string => {
  if (data.length <= visibleChars * 2) {
    return '*'.repeat(data.length);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(data.length - visibleChars * 2);
  
  return `${start}${middle}${end}`;
};

// 验证加密数据的完整性
export const validateEncryptedData = (encryptedText: string): boolean => {
  try {
    // 检查是否是有效的Base64
    const decoded = base64Decode(encryptedText);
    return decoded.length > 0;
  } catch {
    return false;
  }
};