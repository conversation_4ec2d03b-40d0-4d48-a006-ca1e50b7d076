/**
 * ChatBubble Component
 * HER的核心对话气泡组件 - 传递温暖和理解
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  Vibration,
} from 'react-native';
import { defaultTheme, emotionalColors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { chatSpacing, borderRadius, shadows } from '../../theme/spacing';

const { width: screenWidth } = Dimensions.get('window');

export interface ChatBubbleProps {
  // 核心属性
  type: 'her' | 'user';
  message: string;
  timestamp?: string;
  
  // 情感状态
  emotion?: 'neutral' | 'warm' | 'caring' | 'listening' | 'thinking';
  
  // 状态指示
  isTyping?: boolean;
  isFirst?: boolean;  // 是否是一组消息中的第一条
  isLast?: boolean;   // 是否是一组消息中的最后一条
  
  // 交互
  onPress?: () => void;
  onLongPress?: () => void;
}

const ChatBubble: React.FC<ChatBubbleProps> = ({
  type,
  message,
  timestamp,
  emotion = 'neutral',
  isTyping = false,
  isFirst = true,
  isLast = true,
  onPress,
  onLongPress,
}) => {
  // 动画值
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(10)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const typingAnim = useRef(new Animated.Value(0)).current;

  // 入场动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // 输入中动画（呼吸效果）
  useEffect(() => {
    if (isTyping) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(typingAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(typingAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [isTyping]);

  // 长按触感反馈
  const handleLongPress = () => {
    Vibration.vibrate(10); // 轻微震动
    onLongPress?.();
  };

  // 获取气泡颜色
  const getBubbleColor = () => {
    if (type === 'her') {
      switch (emotion) {
        case 'warm': return emotionalColors.caring.bubble;
        case 'caring': return emotionalColors.caring.bubble;
        case 'listening': return emotionalColors.listening.bubble;
        case 'thinking': return emotionalColors.thinking.bubble;
        default: return defaultTheme.bubble.her;
      }
    }
    return defaultTheme.bubble.user;
  };

  // 渲染输入中指示器
  const renderTypingIndicator = () => (
    <Animated.View 
      style={[
        styles.typingIndicator,
        {
          opacity: typingAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 1],
          }),
          transform: [{
            scale: typingAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.95, 1.05],
            }),
          }],
        },
      ]}
    >
      <View style={styles.typingDot} />
      <View style={[styles.typingDot, styles.typingDotMiddle]} />
      <View style={styles.typingDot} />
    </Animated.View>
  );

  // 渲染消息内容
  const renderContent = () => {
    if (isTyping) {
      return renderTypingIndicator();
    }

    return (
      <Text style={[
        styles.messageText,
        type === 'user' && styles.userMessageText,
      ]}>
        {message}
      </Text>
    );
  };

  return (
    <Animated.View
      style={[
        styles.container,
        type === 'user' ? styles.userContainer : styles.herContainer,
        {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        },
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        onLongPress={handleLongPress}
        delayLongPress={500}
      >
        <View
          style={[
            styles.bubble,
            type === 'user' ? styles.userBubble : styles.herBubble,
            {
              backgroundColor: getBubbleColor(),
              borderTopLeftRadius: type === 'her' && !isFirst ? 8 : borderRadius.bubble,
              borderTopRightRadius: type === 'user' && !isFirst ? 8 : borderRadius.bubble,
              borderBottomLeftRadius: type === 'her' && !isLast ? 8 : borderRadius.bubble,
              borderBottomRightRadius: type === 'user' && !isLast ? 8 : borderRadius.bubble,
            },
            isTyping && styles.typingBubble,
          ]}
        >
          {renderContent()}
        </View>
      </TouchableOpacity>
      
      {timestamp && isLast && (
        <Text style={[
          styles.timestamp,
          type === 'user' && styles.userTimestamp,
        ]}>
          {timestamp}
        </Text>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: chatSpacing.betweenMessages / 2,
    paddingHorizontal: chatSpacing.bubblePaddingH,
  },
  
  herContainer: {
    alignItems: 'flex-start',
  },
  
  userContainer: {
    alignItems: 'flex-end',
  },
  
  bubble: {
    maxWidth: screenWidth * 0.75,
    paddingHorizontal: chatSpacing.bubblePaddingH,
    paddingVertical: chatSpacing.bubblePaddingV,
    ...shadows.bubble,
  },
  
  herBubble: {
    marginRight: screenWidth * 0.15,
  },
  
  userBubble: {
    marginLeft: screenWidth * 0.15,
  },
  
  typingBubble: {
    paddingVertical: chatSpacing.bubblePaddingV + 4,
  },
  
  messageText: {
    ...textStyles.chatMessage,
    color: defaultTheme.text.primary,
  },
  
  userMessageText: {
    color: defaultTheme.text.primary,
  },
  
  timestamp: {
    ...textStyles.chatTime,
    color: defaultTheme.text.secondary,
    marginTop: chatSpacing.timeStampMargin,
    marginHorizontal: 4,
  },
  
  userTimestamp: {
    textAlign: 'right',
  },
  
  // 输入中指示器样式
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: defaultTheme.text.secondary,
    opacity: 0.6,
  },
  
  typingDotMiddle: {
    marginHorizontal: 4,
  },
});

export default ChatBubble;