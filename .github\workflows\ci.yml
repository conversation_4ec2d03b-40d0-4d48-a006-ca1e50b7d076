name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20'
  PNPM_VERSION: '8'

jobs:
  lint-and-typecheck:
    name: 代码检查和类型检查
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        
      - name: 安装Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: 安装pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 获取pnpm store目录
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: 缓存pnpm依赖
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
            
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 构建共享包
        run: |
          pnpm shared:build
          pnpm ui:build
          
      - name: 代码检查
        run: pnpm lint
        
      - name: 类型检查
        run: pnpm typecheck
        
      - name: 格式检查
        run: pnpm format:check

  test:
    name: 单元测试
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        
      - name: 安装Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: 安装pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 获取pnpm store目录
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: 缓存pnpm依赖
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
            
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 构建共享包
        run: |
          pnpm shared:build
          pnpm ui:build
          
      - name: 运行测试
        run: pnpm test:ci
        
      - name: 上传覆盖率报告
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  build-mobile:
    name: 移动应用构建检查
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        
      - name: 安装Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: 安装pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 构建共享包
        run: |
          pnpm shared:build
          pnpm ui:build
          
      - name: Expo Doctor
        working-directory: apps/mobile
        run: npx expo doctor

  build-api:
    name: API构建检查
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        
      - name: 安装Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: 安装pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: 安装依赖
        run: pnpm install --frozen-lockfile
        
      - name: 构建共享包
        run: pnpm shared:build
        
      - name: 构建API
        run: pnpm api:build