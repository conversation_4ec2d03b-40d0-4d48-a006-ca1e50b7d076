# HER - 私密AI陪伴应用

> 温暖、私密、智能的AI陪伴应用，基于React Native + Vercel + Supabase架构

## 🏗️ 项目结构

这是一个基于pnpm workspaces的monorepo项目，采用现代化的全栈架构：

```
her/
├── apps/                           # 应用程序
│   ├── mobile/                    # React Native移动应用
│   │   ├── src/
│   │   │   ├── components/       # UI组件
│   │   │   ├── screens/          # 页面组件
│   │   │   ├── navigation/       # 导航配置
│   │   │   ├── stores/           # 状态管理
│   │   │   └── services/         # API服务
│   │   ├── app.config.ts         # Expo配置
│   │   └── package.json
│   └── web/                       # 管理后台（未来功能）
├── packages/                       # 共享包
│   ├── api/                       # Vercel Edge Functions
│   │   ├── api/                  # API端点
│   │   ├── lib/                  # 库文件
│   │   └── middleware/           # 中间件
│   ├── shared/                    # 共享类型和工具
│   │   ├── src/
│   │   │   ├── types/           # TypeScript类型
│   │   │   ├── constants/       # 常量配置
│   │   │   └── utils/           # 工具函数
│   │   └── package.json
│   └── ui/                        # 共享UI组件
│       ├── src/
│       │   ├── components/      # 通用组件
│       │   └── theme/           # 主题系统
│       └── package.json
├── docs/                          # 项目文档
├── scripts/                       # 构建和部署脚本
└── .github/workflows/             # CI/CD配置
```

## 🚀 快速开始

### 自动化安装（推荐）

```bash
# 克隆项目
git clone https://github.com/your-org/her.git
cd her

# 运行自动化安装脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 手动安装

#### 环境要求

- Node.js 20+ LTS
- pnpm 8+
- iOS开发：Xcode 14+ (仅macOS)
- Android开发：Android Studio

#### 安装步骤

```bash
# 1. 安装依赖
pnpm install

# 2. 构建共享包
pnpm shared:build
pnpm ui:build

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 文件，填入必要的API密钥
```

### 开发

```bash
# 启动所有服务
pnpm dev

# 或者分别启动
pnpm mobile:dev    # 启动移动端开发服务器
pnpm api:dev       # 启动API开发服务器（Vercel Dev）
```

#### 移动端开发

```bash
# 进入移动应用目录
cd apps/mobile

# iOS (仅macOS)
pnpm ios

# Android
pnpm android

# Web (快速原型开发)
pnpm web
```

## 📱 技术栈

### 前端架构
- **React Native** 0.73+ with Expo SDK 50
- **TypeScript** 5.3+ 完整类型安全
- **Zustand** 轻量级状态管理
- **React Navigation** 6.x 原生导航
- **React Native Reanimated** 3.x 高性能动画
- **MMKV** 高性能本地存储
- **Expo Secure Store** 安全数据存储

### 后端架构
- **Vercel Edge Functions** Serverless API
- **Supabase** 一站式后端服务
  - PostgreSQL 15 + pgvector
  - 实时订阅
  - 文件存储
  - 用户认证
- **OpenAI API** GPT-4 Turbo 对话
- **Vercel KV** Redis缓存

### 开发工具链
- **pnpm workspaces** Monorepo管理
- **TypeScript** + **Zod** 类型验证
- **ESLint** + **Prettier** 代码规范
- **Jest** + **React Native Testing Library** 测试
- **GitHub Actions** CI/CD

## 🎨 设计系统

HER使用统一的设计令牌系统，确保跨平台的一致性：

### 主题系统
- **默认主题** - 温暖的晨光色调
- **宁静主题** - 平和的蓝色调
- **温暖主题** - 舒适的橙色调  
- **深夜主题** - 护眼的深色调

### 情绪感知色彩
- **倾听状态** - 蓝色系，传达专注和理解
- **思考状态** - 黄色系，展现智慧和温暖
- **关怀状态** - 粉色系，表达爱护和支持
- **理解状态** - 绿色系，体现共情和成长

### 使用方式

```typescript
import { designTokens, useTheme } from '@her/shared';

// 在组件中使用主题
const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <View style={{
      backgroundColor: theme.colors.background,
      padding: designTokens.spacing.md,
      borderRadius: designTokens.borderRadius.card,
    }}>
      <Text style={{ color: theme.colors.text.primary }}>
        Hello HER
      </Text>
    </View>
  );
};
```

## 🏛️ 架构特色

### 隐私优先
- 端到端消息加密
- 本地数据优先存储
- 匿名登录支持
- 符合隐私法规

### 情感计算
- 实时情绪识别
- 上下文记忆系统
- 个性化回复生成
- 情绪趋势分析

### 性能优化
- Vercel边缘计算
- 智能缓存策略
- 懒加载和代码分割
- 原生动画性能

## 🧪 测试策略

```bash
# 运行所有测试
pnpm test

# 带覆盖率的测试
pnpm test:ci

# 特定包测试
pnpm --filter mobile test
pnpm --filter shared test
pnpm --filter api test

# 端到端测试（开发中）
pnpm test:e2e

# 类型检查
pnpm typecheck

# 代码规范检查
pnpm lint
pnpm format:check
```

### 测试覆盖率目标
- 单元测试：> 70%
- 集成测试：> 60%
- 关键路径：> 90%

## 🚀 部署

### 一键部署

```bash
# 预览环境部署
./scripts/deploy.sh preview

# 生产环境部署
./scripts/deploy.sh production
```

### 分步部署

#### API部署（Vercel）
```bash
cd packages/api
vercel --prod  # 生产环境
vercel         # 预览环境
```

#### 移动应用构建
```bash
cd apps/mobile

# EAS构建
eas build --platform all

# 本地构建（开发）
expo run:ios     # iOS
expo run:android # Android
```

### 环境配置

| 环境 | API地址 | 用途 |
|------|---------|------|
| Development | http://localhost:3000 | 本地开发 |
| Preview | https://her-api-preview.vercel.app | 预览测试 |
| Production | https://api.her-app.com | 生产环境 |

## 📚 开发指南

### 新功能开发流程

1. **类型定义**
   ```bash
   # 在 packages/shared/src/types/ 中定义接口
   export interface NewFeature {
     id: string;
     name: string;
   }
   ```

2. **API实现**
   ```bash
   # 在 packages/api/api/ 中创建端点
   export default async function handler(req, res) {
     // 实现逻辑
   }
   ```

3. **UI组件**
   ```bash
   # 在 packages/ui/src/components/ 中创建组件
   export const NewComponent = () => {
     // 组件实现
   };
   ```

4. **移动端集成**
   ```bash
   # 在 apps/mobile/src/ 中集成功能
   import { NewComponent } from '@her/ui';
   ```

### 代码规范

- 使用TypeScript进行严格类型检查
- 遵循ESLint和Prettier配置
- 组件命名使用PascalCase
- 文件名使用camelCase
- 常量使用UPPER_SNAKE_CASE

### Git工作流

```bash
# 功能分支
git checkout -b feature/new-feature

# 提交规范
git commit -m "feat: add new feature"
git commit -m "fix: resolve issue"
git commit -m "docs: update readme"

# 提交前检查
pnpm lint
pnpm test
pnpm typecheck
```

## 🛠️ 常用命令

| 命令 | 描述 |
|------|------|
| `pnpm dev` | 启动所有开发服务 |
| `pnpm build` | 构建所有包 |
| `pnpm test` | 运行所有测试 |
| `pnpm lint` | 代码检查 |
| `pnpm format` | 代码格式化 |
| `pnpm typecheck` | TypeScript类型检查 |
| `pnpm clean` | 清理构建文件 |
| `pnpm reset` | 重置依赖并重新安装 |

### 包级别命令

```bash
# 移动端
pnpm mobile:dev     # 开发服务器
pnpm mobile:ios     # iOS模拟器
pnpm mobile:android # Android模拟器
pnpm mobile:build   # EAS构建

# API
pnpm api:dev        # 本地API服务器
pnpm api:deploy     # 部署到Vercel
pnpm api:logs       # 查看日志

# 共享包
pnpm shared:build   # 构建类型定义
pnpm ui:build      # 构建UI组件
```

## 🔧 故障排除

### 常见问题

**Metro bundler错误**
```bash
pnpm mobile:clean
pnpm install
pnpm shared:build && pnpm ui:build
```

**iOS构建失败**
```bash
cd apps/mobile/ios
pod install
```

**TypeScript错误**
```bash
pnpm typecheck
# 检查packages/shared是否已构建
pnpm shared:build
```

**Vercel部署失败**
```bash
# 检查环境变量
vercel env ls
# 查看构建日志
vercel logs
```

### 开发环境重置

```bash
# 完全重置项目
pnpm clean
rm -rf node_modules
pnpm install
pnpm shared:build
pnpm ui:build
```

## 📊 性能监控

- **Vercel Analytics** - API性能监控
- **Expo Analytics** - 应用使用统计
- **Sentry** - 错误追踪（计划中）
- **自定义指标** - 关键业务指标

## 🔐 安全考虑

- API密钥通过环境变量管理
- 用户数据端到端加密
- 依赖项定期安全扫描
- 遵循OWASP最佳实践

## 📖 文档

- [架构文档](docs/architecture.md) - 详细的技术架构
- [设计文档](docs/design/) - UI/UX设计规范
- [API文档](packages/api/README.md) - API接口说明
- [组件库文档](packages/ui/README.md) - UI组件使用指南

## 🤝 贡献指南

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

### 贡献规范

- 代码必须通过所有测试
- 新功能需要添加测试用例
- 遵循现有的代码风格
- 更新相关文档

## 📄 许可证

本项目采用私有许可证，未经授权不得使用、复制或分发。

---

**HER Team** ❤️ 为现代都市人打造温暖的AI陪伴体验