import { ExpoConfig, ConfigContext } from 'expo/config';

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: '<PERSON><PERSON>',
  slug: 'her-ai-companion',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'automatic',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#FAF7F2', // morningCream
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.her.app',
    buildNumber: '1',
    infoPlist: {
      NSFaceIDUsageDescription: '使用Face ID来保护您的隐私对话',
      NSCameraUsageDescription: '用于设置个人头像',
      NSMicrophoneUsageDescription: '用于语音对话功能',
    },
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#FAF7F2',
    },
    package: 'com.her.app',
    versionCode: 1,
    permissions: [
      'CAMERA',
      'RECORD_AUDIO',
      'USE_BIOMETRIC',
      'USE_FINGERPRINT',
      'VIBRATE',
    ],
  },
  web: {
    favicon: './assets/favicon.png',
    bundler: 'metro',
  },
  plugins: [
    'expo-secure-store',
    [
      'expo-notifications',
      {
        icon: './assets/notification-icon.png',
        color: '#F5E6E0', // twilightPink
        sounds: ['./assets/sounds/notification.wav'],
      },
    ],
    [
      'expo-font',
      {
        fonts: ['./assets/fonts/Inter.ttf', './assets/fonts/Nunito.ttf'],
      },
    ],
  ],
  experiments: {
    typedRoutes: true,
  },
  extra: {
    eas: {
      projectId: 'your-eas-project-id',
    },
  },
});