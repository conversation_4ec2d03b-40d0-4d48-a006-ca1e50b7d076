/**
 * 通用错误处理工具
 * 提供常用的错误处理和转换函数，减少重复代码
 */

import { ZodError } from 'zod';
import { StandardAPIError, APIErrors } from '../middleware/error-handler';

/**
 * 错误分类器
 */
export class ErrorClassifier {
  /**
   * 分类并转换错误为StandardAPIError
   */
  static classify(error: any): StandardAPIError {
    // Zod验证错误
    if (error instanceof ZodError) {
      return APIErrors.VALIDATION_ERROR(
        '请求参数验证失败',
        error.errors.map((e) => ({
          path: e.path.join('.'),
          message: e.message,
          code: e.code,
        }))
      );
    }

    // JWT相关错误
    if (error.name === 'JsonWebTokenError') {
      return APIErrors.AUTH_INVALID('JWT令牌格式错误');
    }

    if (error.name === 'TokenExpiredError') {
      return APIErrors.AUTH_EXPIRED('JWT令牌已过期');
    }

    // OpenAI/OpenRouter API错误
    if (error.name === 'APIError' && error.status) {
      return ErrorClassifier.classifyAIError(error);
    }

    // Supabase错误
    if (error.code && typeof error.code === 'string') {
      return ErrorClassifier.classifySupabaseError(error);
    }

    // Vercel KV错误
    if (error.message?.includes('KV') || error.message?.includes('Redis')) {
      return APIErrors.INTERNAL_ERROR('缓存服务暂时不可用', {
        originalError: error.message,
      });
    }

    // 网络/连接错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return APIErrors.AI_SERVICE_ERROR('外部服务连接失败', {
        code: error.code,
        message: error.message,
      });
    }

    // 标准API错误直接返回
    if (error instanceof StandardAPIError) {
      return error;
    }

    // 默认内部错误
    return APIErrors.INTERNAL_ERROR(
      error.message || '服务器内部错误',
      process.env.NODE_ENV === 'development' ? {
        stack: error.stack,
        name: error.name,
      } : undefined
    );
  }

  /**
   * 分类AI服务错误
   */
  private static classifyAIError(error: any): StandardAPIError {
    const status = error.status || 500;
    const message = error.message || 'AI服务错误';

    switch (status) {
      case 400:
        return APIErrors.BAD_REQUEST(`AI服务参数错误: ${message}`);
      
      case 401:
        return APIErrors.AI_SERVICE_ERROR('AI服务认证失败', {
          hint: '请检查OPENROUTER_API_KEY配置',
        });
      
      case 429:
        return APIErrors.RATE_LIMITED('AI服务请求过于频繁，请稍后重试');
      
      case 500:
      case 502:
      case 503:
        return APIErrors.AI_SERVICE_ERROR('AI服务暂时不可用', {
          status,
          retryAfter: 30,
        });
      
      default:
        return APIErrors.AI_SERVICE_ERROR(`AI服务错误: ${message}`, { status });
    }
  }

  /**
   * 分类Supabase数据库错误
   */
  private static classifySupabaseError(error: any): StandardAPIError {
    const code = error.code;
    const message = error.message || '数据库操作失败';

    switch (code) {
      case 'PGRST301':
        return APIErrors.NOT_FOUND('请求的数据不存在');
      
      case 'PGRST204':
        return APIErrors.FORBIDDEN('权限不足，无法访问数据');
      
      case '23505': // unique_violation
        return APIErrors.BAD_REQUEST('数据已存在，请检查重复项');
      
      case '23503': // foreign_key_violation
        return APIErrors.BAD_REQUEST('数据关联错误，请检查引用关系');
      
      case '23502': // not_null_violation
        return APIErrors.VALIDATION_ERROR('必填字段不能为空');
      
      case '42601': // syntax_error
        return APIErrors.INTERNAL_ERROR('数据库查询语法错误', {
          code,
          query: error.details,
        });
      
      default:
        return APIErrors.DATABASE_ERROR(`数据库错误: ${message}`, {
          code,
          hint: error.hint,
        });
    }
  }
}

/**
 * 通用错误处理器
 */
export class ErrorHandler {
  /**
   * 安全地处理异步操作，返回结果或错误
   */
  static async safeAsync<T>(
    operation: () => Promise<T>
  ): Promise<[T | null, StandardAPIError | null]> {
    try {
      const result = await operation();
      return [result, null];
    } catch (error) {
      const standardError = ErrorClassifier.classify(error);
      return [null, standardError];
    }
  }

  /**
   * 安全地处理同步操作
   */
  static safe<T>(
    operation: () => T
  ): [T | null, StandardAPIError | null] {
    try {
      const result = operation();
      return [result, null];
    } catch (error) {
      const standardError = ErrorClassifier.classify(error);
      return [null, standardError];
    }
  }

  /**
   * 重试机制包装器
   */
  static async retry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000,
    backoffMultiplier: number = 2
  ): Promise<T> {
    let lastError: any;
    let delay = delayMs;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        // 最后一次尝试失败，抛出错误
        if (attempt === maxRetries) {
          break;
        }

        // 某些错误不需要重试
        if (ErrorHandler.shouldNotRetry(error)) {
          break;
        }

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= backoffMultiplier;
      }
    }

    throw ErrorClassifier.classify(lastError);
  }

  /**
   * 判断是否不应该重试的错误
   */
  private static shouldNotRetry(error: any): boolean {
    // 4xx错误通常不需要重试
    if (error.status >= 400 && error.status < 500) {
      return true;
    }

    // 验证错误不需要重试
    if (error instanceof ZodError) {
      return true;
    }

    // JWT错误不需要重试
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return true;
    }

    return false;
  }
}

/**
 * 上下文错误处理器
 */
export class ContextualErrorHandler {
  private context: string;
  private userId?: string;
  private requestId?: string;

  constructor(context: string, userId?: string, requestId?: string) {
    this.context = context;
    this.userId = userId;
    this.requestId = requestId;
  }

  /**
   * 处理错误并添加上下文信息
   */
  handle(error: any): StandardAPIError {
    const standardError = ErrorClassifier.classify(error);
    
    // 记录错误日志
    this.logError(error, standardError);
    
    return standardError;
  }

  /**
   * 记录带上下文的错误
   */
  private logError(originalError: any, standardError: StandardAPIError): void {
    const logContext = {
      context: this.context,
      userId: this.userId,
      requestId: this.requestId,
      error: {
        code: standardError.code,
        message: standardError.message,
        statusCode: standardError.statusCode,
      },
      originalError: {
        name: originalError.name,
        message: originalError.message,
        stack: originalError.stack,
      },
      timestamp: new Date().toISOString(),
    };

    if (standardError.statusCode >= 500) {
      console.error(`[${this.context}] Server Error:`, JSON.stringify(logContext, null, 2));
    } else {
      console.warn(`[${this.context}] Client Error:`, JSON.stringify(logContext, null, 2));
    }
  }
}

/**
 * 便捷的错误处理函数
 */

/**
 * 创建上下文错误处理器
 */
export function createErrorHandler(
  context: string,
  userId?: string,
  requestId?: string
): ContextualErrorHandler {
  return new ContextualErrorHandler(context, userId, requestId);
}

/**
 * 快速错误分类
 */
export function classifyError(error: any): StandardAPIError {
  return ErrorClassifier.classify(error);
}

/**
 * 安全异步操作
 */
export async function safeAsync<T>(
  operation: () => Promise<T>
): Promise<[T | null, StandardAPIError | null]> {
  return ErrorHandler.safeAsync(operation);
}

/**
 * 重试异步操作
 */
export async function retryAsync<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  return ErrorHandler.retry(operation, maxRetries, delayMs);
}

/**
 * 创建API错误的快捷方式
 */
export const createAPIError = {
  badRequest: (message: string, details?: any) => APIErrors.BAD_REQUEST(message, details),
  unauthorized: (message?: string) => APIErrors.AUTH_INVALID(message),
  forbidden: (message?: string) => APIErrors.FORBIDDEN(message),
  notFound: (message?: string) => APIErrors.NOT_FOUND(message),
  rateLimit: (message?: string) => APIErrors.RATE_LIMITED(message),
  validation: (message: string, details?: any) => APIErrors.VALIDATION_ERROR(message, details),
  aiService: (message?: string, details?: any) => APIErrors.AI_SERVICE_ERROR(message, details),
  database: (message?: string, details?: any) => APIErrors.DATABASE_ERROR(message, details),
  internal: (message?: string, details?: any) => APIErrors.INTERNAL_ERROR(message, details),
};