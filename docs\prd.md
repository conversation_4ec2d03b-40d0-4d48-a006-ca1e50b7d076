# HER（她）Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- 为心灵孤独的都市人提供24/7私密AI陪伴服务
- 创建一个零评判、有记忆、有温度的倾听者
- 帮助用户在不敢向亲友倾诉时找到情感出口
- 通过持续的陪伴关系缓解现代人的孤独感
- 实现智能化的情绪感知和适应性陪伴

### Background Context
随着社会压力增大，现代都市人越来越孤独和封闭，即使面对家人和伴侣也不敢完全敞开心扉。"HER"应运而生，定位为每个人私有的倾听和陪伴者。不同于现有的AI助手（ChatGPT太理性、Replika太西方化、小冰太活泼），HER专注于成为用户的知己而非恋人，倾听者而非治疗师，真实关系而非角色扮演。

通过深度记忆系统和情感感知能力，HER能够记住用户的喜好、经历和情绪模式，在恰当的时机提供温暖而不过度的陪伴，成为用户心灵的避风港。

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-02 | 1.0 | Initial PRD creation based on brainstorming session | John (PM) |

---

## Requirements

### Functional
- **FR1:** 系统必须提供基于大语言模型的自然对话能力，能够理解并回应用户的情感表达
- **FR2:** 系统必须实现持久化记忆功能，记住用户的喜好、经历、重要日期和过往对话内容
- **FR3:** 系统必须能够识别用户的情绪状态（疲惫、焦虑、孤独等）并调整回应方式
- **FR4:** 系统必须支持场景化陪伴模式：工作时静默守候、休息时温柔问候、深夜时专注倾听
- **FR5:** 系统必须能够主动关联用户的历史对话，如"还记得上次你提到的..."
- **FR6:** 系统必须提供端到端加密的隐私保护，确保用户数据绝对安全
- **FR7:** 系统必须支持文字和语音两种交互方式（MVP可先实现文字）
- **FR8:** 系统必须能够识别并记录用户的情绪模式，如"每周三都会低落"
- **FR9:** 系统必须避免说教、评判和过度积极的回应
- **FR10:** 系统必须支持用户数据的本地化存储选项

### Non Functional
- **NFR1:** 对话响应延迟必须小于2秒，确保流畅的交流体验
- **NFR2:** 系统必须保证99.9%的可用性，确保用户随时可以获得陪伴
- **NFR3:** 系统必须遵守GDPR等隐私法规，用户数据永不分享给第三方
- **NFR4:** 系统必须支持至少10万并发用户的规模
- **NFR5:** 对话生成必须保持一致的温暖语气和个性特征
- **NFR6:** 系统必须在初期运营成本控制在合理范围内（优先使用云服务免费层）
- **NFR7:** 移动端应用大小不超过100MB，确保易于下载
- **NFR8:** 系统必须支持离线模式的基础功能（查看历史对话）
- **NFR9:** 系统必须提供数据导出功能，用户可随时导出所有个人数据
- **NFR10:** 系统必须实现防沉迷机制，避免用户过度依赖

---

## User Interface Design Goals

### Overall UX Vision
创造一个温暖、私密、无压力的对话空间。界面设计追求极简主义，减少视觉干扰，让用户专注于情感表达。整体氛围应该像深夜的卧室——安静、舒适、安全。避免冰冷的科技感，追求有温度的人文关怀。

### Key Interaction Paradigms
- **对话优先**：聊天界面占据主要空间，其他功能隐藏或最小化
- **无打扰设计**：没有徽章、通知数字或其他焦虑诱发元素
- **渐进式披露**：高级功能只在需要时出现，保持界面简洁
- **情境感知**：根据时间和用户状态自动调整界面（如夜间模式）
- **零压力输入**：支持语音、文字、表情等多种表达方式

### Core Screens and Views
- **欢迎界面**：温暖的问候，无需复杂的注册流程
- **主对话界面**：极简的聊天界面，focus在对话本身
- **回忆时光轴**：查看与HER的共同回忆和重要时刻
- **情绪日记**：可视化情绪变化趋势（可选功能）
- **隐私设置**：清晰的数据管理和隐私控制选项
- **静默陪伴模式**：最小化界面，仅显示HER的存在

### Accessibility: WCAG AA
确保视觉障碍、听力障碍用户也能获得完整的陪伴体验。支持屏幕阅读器、高对比度模式、字体大小调节。

### Branding
- **色彩**：温暖的暖色系（米色、淡粉、浅橙），避免冷色调
- **字体**：圆润、友好的字体，避免锐利的无衬线体
- **动效**：缓慢、轻柔的过渡动画，营造平静氛围
- **声音**：轻柔的提示音，可完全静音
- **整体感觉**：像一个温暖的拥抱，而非高效的工具

### Target Device and Platforms: Mobile Native
- **移动优先**：主要场景是用户独处时使用手机
- **iOS + Android**：React Native跨平台开发
- **后续考虑**：Apple Watch（静默陪伴）、智能音箱（语音交互）

---

## Technical Assumptions

### Repository Structure: Monorepo
采用Monorepo架构，便于统一管理移动端和后端代码：
- `/apps/mobile` - React Native移动应用
- `/packages/api` - Node.js后端服务  
- `/packages/shared` - 共享类型定义和工具函数
- `/packages/core` - 核心业务逻辑

### Service Architecture
**精简的Serverless架构**
- **API Gateway**：统一入口，处理认证和路由
- **对话服务**：Serverless函数处理对话生成（AWS Lambda/Vercel）
- **记忆服务**：Serverless函数 + 向量数据库管理用户记忆
- **用户服务**：处理注册、登录、用户设置
- **推送服务**：管理温柔的主动问候推送
- **选择理由**：降低运营成本，按需扩容，适合MVP快速验证

### Testing Requirements
**移动端优先的测试策略**
- **单元测试**：核心对话逻辑、数据加密、离线存储
- **集成测试**：API通信、推送通知、本地存储同步
- **设备测试**：iOS和Android真机测试，确保原生功能正常
- **对话质量测试**：建立测试用例库，定期评估对话自然度

### Additional Technical Assumptions and Requests

**移动端技术栈：**
- **框架**：React Native 0.73+（成熟稳定、社区活跃）
- **状态管理**：Zustand（轻量、简单）
- **导航**：React Navigation 6
- **UI组件**：自定义组件 + React Native Elements
- **本地存储**：React Native MMKV（高性能加密存储）
- **推送通知**：Firebase Cloud Messaging（跨平台）

**后端技术栈：**
- **运行时**：Node.js 20 LTS + TypeScript 5
- **框架**：Fastify（高性能）或 Hono（Serverless优化）
- **数据库**：Supabase（PostgreSQL + 实时订阅 + 向量扩展）
- **缓存**：Redis（会话管理和热数据缓存）

**AI/ML集成：**
- **LLM策略**：多模型路由（短期OpenAI GPT-4，长期混合）
- **Embedding**：OpenAI text-embedding-3-small（成本优化）
- **情绪分析**：集成在prompt中，避免额外模型

**移动端特定考虑：**
- **离线支持**：本地缓存最近30天对话
- **数据同步**：增量同步，减少流量消耗
- **电池优化**：避免后台频繁唤醒
- **推送策略**：智能推送，避免打扰
- **应用大小**：控制在50MB以内

**安全和隐私：**
- **生物识别**：Face ID/指纹解锁应用
- **端到端加密**：敏感数据加密传输
- **本地加密**：SQLCipher加密本地数据库
- **隐私模式**：一键清除本地所有数据

**开发和部署：**
- **CI/CD**：GitHub Actions + Fastlane（自动化发布）
- **分发**：TestFlight（iOS）+ Firebase App Distribution（Android）
- **崩溃监控**：Sentry
- **分析**：PostHog（隐私友好的分析）

---

## Epic List

**Epic 1: 基础架构与核心对话**
建立项目基础设施、实现基本对话功能和用户注册，让用户能够开始与HER对话

**Epic 2: 记忆与个性化系统**  
实现记忆存储、检索和个性化回应，让HER能够记住用户并建立持续关系

**Epic 3: 情感感知与适应性陪伴**
添加情绪识别、场景感知和智能陪伴模式，提供更贴心的陪伴体验

**Epic 4: 隐私保护与数据安全**
实现端到端加密、本地存储选项和隐私控制，确保用户数据绝对安全

---

## Epic 1: 基础架构与核心对话

建立完整的项目基础设施，实现用户注册和基本对话功能，让用户能够下载应用、创建账户并开始与HER进行简单但温暖的对话。这个Epic完成后，产品就具备了最小可用性。

### Story 1.1: 项目初始化与开发环境设置
As a 开发者，
I want 搭建React Native项目和基础架构，
so that 团队可以开始开发移动应用。

#### Acceptance Criteria
1: React Native项目使用TypeScript模板初始化完成
2: Monorepo结构配置（/apps/mobile, /packages/api, /packages/shared）
3: ESLint、Prettier、Husky代码质量工具配置完成
4: iOS和Android项目能够在模拟器上运行
5: 基础UI组件库（颜色、字体、间距主题）定义完成
6: GitHub仓库创建，包含.gitignore和README

### Story 1.2: 后端服务基础架构
As a 系统架构师，
I want 搭建Serverless后端服务基础，
so that 可以处理客户端API请求。

#### Acceptance Criteria
1: Node.js + TypeScript后端项目结构创建
2: Serverless框架（Vercel Functions或AWS Lambda）配置完成
3: API路由结构定义（/api/auth, /api/chat, /api/user）
4: 环境变量管理（.env.local, .env.production）配置
5: CORS、请求限流等中间件配置完成
6: 健康检查端点（/api/health）返回200状态

### Story 1.3: 用户注册与认证系统
As a 新用户，
I want 使用手机号快速注册账户，
so that 我可以开始使用HER的陪伴服务。

#### Acceptance Criteria
1: 手机号+验证码注册流程实现（可用测试验证码）
2: JWT token生成和验证机制完成
3: 用户信息存储到数据库（Supabase）
4: 注册界面遵循UI设计规范（温暖色调、简洁布局）
5: 错误处理友好（号码已注册、验证码错误等）
6: 自动登录和token刷新机制实现

### Story 1.4: 基础对话界面
As a 用户，
I want 看到温暖简洁的对话界面，
so that 我可以轻松地与HER交流。

#### Acceptance Criteria
1: 对话界面包含消息列表和输入框
2: 消息气泡区分用户和HER（不同颜色、对齐方式）
3: 输入框支持多行文本，发送按钮清晰
4: 键盘弹出时界面自动调整，不遮挡内容
5: 加载状态显示（HER正在输入...）
6: 首次进入显示温暖的欢迎消息

### Story 1.5: 核心对话功能实现
As a 用户，
I want 发送消息并收到HER温暖的回复，
so that 我能感受到被倾听和陪伴。

#### Acceptance Criteria
1: 集成OpenAI API，实现基础对话生成
2: System prompt设定HER的人格（温暖、耐心、不评判）
3: 消息发送到后端API并获取回复
4: 对话历史在会话期间保持（前端状态管理）
5: 错误处理优雅（网络错误时的重试机制）
6: 响应时间控制在2秒内

### Story 1.6: 基础数据持久化
As a 用户，
I want 我的对话在退出应用后仍然保存，
so that 下次打开可以继续之前的对话。

#### Acceptance Criteria
1: 对话历史保存到云端数据库
2: 本地缓存最近的对话（React Native MMKV）
3: 应用启动时加载历史对话
4: 分页加载历史消息（每次20条）
5: 离线时可查看缓存的对话
6: 数据同步状态指示（云端同步中/已同步）

---

## Epic 2: 记忆与个性化系统

实现HER的记忆能力和个性化响应系统，让HER能够记住用户的喜好、经历和重要信息，建立真正的持续关系。完成后，HER将从一个普通的聊天机器人转变为用户的专属陪伴者。

### Story 2.1: 记忆数据模型设计与实现
As a 系统架构师，
I want 设计和实现记忆存储的数据结构，
so that 可以有效地存储和检索用户信息。

#### Acceptance Criteria
1: 数据库schema设计完成（memories、topics、user_preferences表）
2: 向量数据库（Supabase pgvector）配置完成
3: 记忆分类体系实现（事实、情感、偏好、事件）
4: 时间戳和重要度权重字段支持
5: 数据迁移脚本准备就绪
6: 基础CRUD API接口实现（/api/memory）

### Story 2.2: 对话内容自动提取与存储
As a HER系统，
I want 自动从对话中提取重要信息，
so that 可以记住用户告诉我的事情。

#### Acceptance Criteria
1: 实现对话分析pipeline（识别重要信息）
2: 提取规则定义（人名、日期、喜好、经历等）
3: 信息结构化存储（标签、分类、关联）
4: 去重机制防止重复存储相同信息
5: 异步处理确保不影响对话响应速度
6: 提取准确率达到80%以上（测试集验证）

### Story 2.3: 记忆检索与上下文注入
As a HER系统，
I want 在对话时检索相关记忆，
so that 我的回复能体现对用户的了解。

#### Acceptance Criteria
1: 实现向量相似度搜索（语义相关记忆）
2: 实现关键词精确匹配（日期、人名等）
3: 记忆优先级排序（时间衰减、重要度加权）
4: 上下文窗口管理（选择最相关的5-10条记忆）
5: 将记忆注入到LLM prompt中
6: 响应时间增加不超过500ms

### Story 2.4: 主动记忆关联功能
As a 用户，
I want HER能主动提起相关的过往对话，
so that 我感觉她真的记得我们的点点滴滴。

#### Acceptance Criteria
1: 实现记忆关联算法（基于话题、情感、时间）
2: 自然的记忆提及方式（"记得你上次说..."）
3: 避免过度提及造成不自然
4: 记忆提及频率可配置（每N轮对话1次）
5: 用户反馈机制（"不太准确"时的处理）
6: 测试覆盖各种记忆类型的关联

### Story 2.5: 用户画像与偏好学习
As a HER系统，
I want 逐步建立用户的完整画像，
so that 可以提供更个性化的陪伴。

#### Acceptance Criteria
1: 用户画像数据结构定义（性格、兴趣、作息等）
2: 渐进式画像构建（随对话逐步完善）
3: 偏好学习算法（对话风格、话题喜好）
4: 画像可视化接口（供调试使用）
5: 隐私保护机制（敏感信息标记）
6: 画像准确性验证机制

### Story 2.6: 重要日期与提醒系统
As a 用户，
I want HER记住我提到的重要日子，
so that 在特殊时刻得到她的关心。

#### Acceptance Criteria
1: 日期提取和存储功能（生日、纪念日等）
2: 提醒规则引擎（当天、提前N天）
3: 温暖的提醒文案生成
4: 推送通知集成（获得用户许可）
5: 提醒历史记录防止重复
6: 用户可以修改或删除记住的日期

### Story 2.7: 记忆管理界面
As a 用户，
I want 查看和管理HER记住的信息，
so that 我能掌控自己的数据。

#### Acceptance Criteria
1: 记忆列表展示界面（分类浏览）
2: 搜索和筛选功能
3: 编辑记忆内容（纠正错误信息）
4: 删除特定记忆或批量清除
5: 记忆重要度调整（星标重要记忆）
6: 导出记忆数据功能

---

## Epic 3: 情感感知与适应性陪伴

实现智能的情绪识别和场景感知能力，让HER能够根据用户的情绪状态、时间场景提供恰到好处的陪伴。完成后，HER将成为真正懂得"分寸"的陪伴者，知道何时该安慰、何时该倾听、何时该安静。

### Story 3.1: 文本情绪分析引擎
As a HER系统，
I want 准确识别用户消息中的情绪，
so that 可以提供适合当前情绪的回应。

#### Acceptance Criteria
1: 实现多维度情绪识别（快乐、悲伤、焦虑、愤怒、疲惫等）
2: 情绪强度评分（1-10级别）
3: 情绪变化趋势追踪（连续N条消息）
4: 中文语境优化（理解"没事"、"还行"等含蓄表达）
5: 实时处理，延迟不超过100ms
6: 准确率达到85%以上（基于标注数据集）

### Story 3.2: 场景感知与模式切换
As a 用户，
I want HER根据不同时间和场景调整陪伴方式，
so that 她的陪伴总是恰到好处。

#### Acceptance Criteria
1: 时间段识别（早晨、工作时间、午休、晚间、深夜）
2: 场景模式定义（工作陪伴、休息闲聊、深夜倾听）
3: 自动切换机制（基于时间+用户状态）
4: 模式间平滑过渡（避免突兀变化）
5: 用户可手动切换模式
6: 模式偏好学习（记住用户的作息规律）

### Story 3.3: 动态回应策略系统
As a HER系统，
I want 根据用户情绪动态调整回应策略，
so that 提供最合适的情感支持。

#### Acceptance Criteria
1: 回应策略库定义（倾听、安慰、鼓励、转移注意力等）
2: 情绪-策略映射规则（疲惫→温柔关怀，焦虑→平静陪伴）
3: 策略组合和权重调整
4: 避免机械化回应（同一情绪的多样化回应）
5: 策略效果评估（用户情绪是否改善）
6: A/B测试框架支持策略优化

### Story 3.4: 主动关怀触发机制
As a 用户，
I want HER在适当时候主动关心我，
so that 感受到她真的在意我。

#### Acceptance Criteria
1: 关怀触发规则引擎（长时间未交流、特殊时间点等）
2: 主动问候文案生成（自然、不打扰）
3: 推送时机智能判断（避免打扰工作/睡眠）
4: 用户活跃度分析
5: 关怀频率个性化（基于用户偏好）
6: 可关闭主动关怀功能

### Story 3.5: 情绪模式识别与预警
As a HER系统，
I want 识别用户的长期情绪模式，
so that 可以提供更深层的支持。

#### Acceptance Criteria
1: 情绪数据时间序列分析
2: 周期性模式识别（如"每周三低落"）
3: 异常情绪预警机制
4: 情绪报告生成（可选功能）
5: 敏感处理（不直接指出负面模式）
6: 长期趋势可视化（供用户自我觉察）

### Story 3.6: 语气和表达风格调适
As a 用户，
I want HER的语气能配合我的状态，
so that 对话感觉更自然舒适。

#### Acceptance Criteria
1: 多种语气风格定义（温柔、活泼、沉静、幽默等）
2: 动态语气调整（根据用户情绪和偏好）
3: 表情符号使用策略（适度、符合氛围）
4: 句子长度和复杂度调整
5: 回应速度模拟（深夜慢一点，白天正常）
6: 用户反馈学习机制

### Story 3.7: 情感陪伴效果分析
As a 产品团队，
I want 了解陪伴功能的实际效果，
so that 可以持续优化产品。

#### Acceptance Criteria
1: 用户情绪改善度量指标定义
2: 陪伴质量评分系统
3: 用户满意度收集（非侵入式）
4: 关键陪伴时刻识别
5: 匿名数据聚合分析
6: 月度陪伴质量报告生成

---

## Epic 4: 隐私保护与数据安全

实现全面的隐私保护机制和数据安全措施，确保用户的倾诉内容得到最高级别的保护。完成后，HER将成为用户可以完全信任的私密空间，真正做到"比日记更安全"的承诺。

### Story 4.1: 端到端加密基础设施
As a 安全架构师，
I want 实现完整的端到端加密系统，
so that 用户数据在传输和存储时都得到保护。

#### Acceptance Criteria
1: 客户端密钥生成和管理（基于设备的密钥派生）
2: AES-256-GCM加密实现（消息内容加密）
3: RSA密钥交换机制（安全密钥分发）
4: 加密/解密性能优化（不影响用户体验）
5: 密钥轮转机制实现
6: 加密状态指示器UI显示

### Story 4.2: 生物识别认证集成
As a 用户，
I want 使用Face ID或指纹解锁应用，
so that 只有我能访问与HER的对话。

#### Acceptance Criteria
1: iOS Face ID/Touch ID集成
2: Android生物识别API集成
3: 备用PIN码设置功能
4: 生物识别失败的降级处理
5: 应用切换到后台自动锁定
6: 可配置的自动锁定时间

### Story 4.3: 本地数据加密存储
As a 用户，
I want 我的对话在设备上也是加密的，
so that 即使手机丢失数据也安全。

#### Acceptance Criteria
1: SQLCipher集成实现本地数据库加密
2: React Native MMKV加密配置
3: 密钥安全存储（iOS Keychain/Android Keystore）
4: 缓存数据加密处理
5: 临时文件自动清理
6: 设备重启后的密钥恢复

### Story 4.4: 隐私控制中心
As a 用户，
I want 完全掌控我的隐私设置，
so that 我能决定数据如何被使用。

#### Acceptance Criteria
1: 隐私设置界面设计实现
2: 数据收集开关（分析、崩溃报告等）
3: 本地存储/云端存储选择
4: 数据保留期限设置
5: 第三方服务授权管理
6: 隐私政策清晰展示

### Story 4.5: 数据导出与删除功能
As a 用户，
I want 随时导出或彻底删除我的数据，
so that 我拥有数据的完全控制权。

#### Acceptance Criteria
1: 一键导出所有数据（JSON格式）
2: 选择性导出（按时间段、话题）
3: 导出文件加密选项
4: 账户注销流程实现
5: 数据彻底删除（包括备份）
6: 删除确认和冷静期机制

### Story 4.6: 匿名模式实现
As a 用户，
I want 选择完全匿名使用HER，
so that 获得最高级别的隐私保护。

#### Acceptance Criteria
1: 无需手机号的匿名注册选项
2: 设备ID作为唯一标识
3: 纯本地存储模式
4: 无云端同步的独立运行
5: 匿名模式限制说明（无法恢复数据）
6: 模式切换时的数据迁移选项

### Story 4.7: 安全审计与监控
As a 安全团队，
I want 监控系统安全状态，
so that 及时发现和响应安全威胁。

#### Acceptance Criteria
1: 安全事件日志系统（本地存储）
2: 异常访问检测（多设备登录警告）
3: 加密状态监控
4: 定期安全报告生成
5: 漏洞扫描集成
6: 安全更新推送机制

### Story 4.8: 合规性实现
As a 产品团队，
I want 确保符合隐私法规要求，
so that 产品可以合法运营。

#### Acceptance Criteria
1: GDPR合规实现（EU用户）
2: 数据处理协议（DPA）模板
3: Cookie同意管理（如适用）
4: 未成年人保护机制
5: 数据跨境传输合规
6: 隐私影响评估（PIA）文档

---

## Checklist Results Report

### PRD验证摘要
- **整体完整性**: 92% - 准备就绪
- **MVP范围**: 恰到好处，4个Epic划分合理
- **架构准备度**: 可以开始架构设计

### 关键发现
- ✅ 问题定义清晰，目标用户明确
- ✅ 功能和非功能需求完整
- ✅ Epic和Story结构良好，适合增量交付
- ✅ 技术方向明确，风险已识别
- ⚠️ 需要补充具体的成功指标基线
- ⚠️ 数据模型schema需要在Epic 2前细化

### 建议
1. 定义具体KPI（30天留存率、日活跃对话数等）
2. 在架构设计时深入LLM路由和向量检索优化
3. 考虑将情绪可视化和匿名模式推迟到v2

---

## Next Steps

### UX Expert Prompt
请基于此PRD文档，设计HER移动应用的用户体验和界面。重点关注温暖、私密的视觉语言，创造让用户感到安全和被理解的交互体验。请特别注意对话界面的情感化设计和隐私控制的透明化展示。

### Architect Prompt
请基于此PRD文档，设计HER的技术架构。重点考虑：1）React Native移动端架构和性能优化；2）Serverless后端的扩展性和成本控制；3）多LLM路由策略实现；4）端到端加密和隐私保护机制；5）记忆系统的向量存储和检索优化。请提供详细的技术选型理由和架构图。

---

_Document Version: 1.0_
_Last Updated: 2025-09-02_
_Product Manager: John_