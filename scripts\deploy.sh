#!/bin/bash

# HER 项目部署脚本

set -e

echo "🚀 HER 项目部署开始..."

# 检查环境参数
ENVIRONMENT=${1:-"preview"}

if [ "$ENVIRONMENT" != "preview" ] && [ "$ENVIRONMENT" != "production" ]; then
    echo "❌ 无效的环境参数。使用: ./deploy.sh [preview|production]"
    exit 1
fi

echo "🎯 部署环境: $ENVIRONMENT"

# 检查必需的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    fi
}

check_tool "vercel"
check_tool "eas"

# 构建项目
echo "🔨 构建项目..."
pnpm build

# 部署API到Vercel
echo "☁️ 部署API到Vercel..."
cd packages/api

if [ "$ENVIRONMENT" = "production" ]; then
    vercel --prod --yes
else
    vercel --yes
fi

cd ../..

# 构建移动应用
echo "📱 构建移动应用..."
cd apps/mobile

if [ "$ENVIRONMENT" = "production" ]; then
    # 生产环境构建
    echo "🏭 构建生产版本..."
    eas build --platform all --non-interactive
    
    # 可选：自动提交到应用商店
    read -p "是否提交到应用商店? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        eas submit --platform all --non-interactive
    fi
else
    # 预览环境构建
    echo "👀 构建预览版本..."
    eas build --platform all --non-interactive --profile preview
fi

cd ../..

echo ""
echo "🎉 部署完成！"
echo ""

if [ "$ENVIRONMENT" = "production" ]; then
    echo "🌟 生产环境已更新"
    echo "📱 移动应用构建完成，检查EAS控制台获取下载链接"
else
    echo "👀 预览环境已更新"
    echo "📱 预览版本构建完成"
fi

echo "☁️ API已部署到Vercel"
echo "🔍 检查部署状态: vercel ls"