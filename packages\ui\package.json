{"name": "@her/ui", "version": "1.0.0", "private": true, "description": "HER共享UI组件库", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build", "dev": "tsc --build --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "test:ci": "jest --ci --coverage"}, "dependencies": {"@her/shared": "workspace:*", "react": "^18.2.0", "react-native": "^0.73.2"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/react": "^18.2.45", "@types/react-native": "^0.72.8", "jest": "^29.7.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": ">=18.0.0", "react-native": ">=0.73.0"}}