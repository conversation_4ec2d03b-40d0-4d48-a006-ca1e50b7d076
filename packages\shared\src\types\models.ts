/**
 * HER 数据模型类型定义
 * 基于架构文档的数据模型
 */

// 用户模型
export interface User {
  id: string;
  createdAt: Date;
  phone?: string;
  nickname?: string;
  avatarUrl?: string;
  preferences: UserPreferences;
  isAnonymous: boolean;
  lastSeenAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  notificationEnabled: boolean;
  voiceEnabled: boolean;
  language: string;
  emotionalAdaptation: boolean;
  dataRetention: number; // 天数
}

// 对话模型
export interface Conversation {
  id: string;
  userId: string;
  title?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: ConversationMetadata;
  isArchived: boolean;
}

export interface ConversationMetadata {
  messageCount: number;
  lastMessageAt: Date;
  emotion?: EmotionType;
  tags?: string[];
  summary?: string;
}

// 消息模型
export interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant';
  content: string; // 加密后的内容
  createdAt: Date;
  metadata: MessageMetadata;
}

export interface MessageMetadata {
  emotion?: EmotionType;
  tokens?: number;
  model?: string;
  isTyping?: boolean;
  sourceMemories?: string[];
}

// 记忆模型
export interface Memory {
  id: string;
  userId: string;
  type: MemoryType;
  content: string;
  embedding: number[]; // 1536维向量
  importance: number; // 0-1
  createdAt: Date;
  accessedAt: Date;
  metadata: MemoryMetadata;
}

export interface MemoryMetadata {
  source?: string;
  tags?: string[];
  relatedMemories?: string[];
  context?: string;
}

export type MemoryType = 'fact' | 'preference' | 'event' | 'emotion';

// 情绪签到模型
export interface MoodCheckIn {
  id: string;
  userId: string;
  moodValue: number; // 0-100
  moodLabel: string;
  createdAt: Date;
  note?: string;
  triggers?: string[];
}

// 情绪类型
export type EmotionType = 
  | 'neutral'
  | 'warm'
  | 'caring'
  | 'listening'
  | 'thinking'
  | 'understanding'
  | 'excited'
  | 'concerned'
  | 'supportive';

// 消息状态
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

// 对话状态
export type ConversationStatus = 'active' | 'archived' | 'deleted';