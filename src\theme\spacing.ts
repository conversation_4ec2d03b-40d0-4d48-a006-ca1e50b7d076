/**
 * HER Spacing System
 * 和谐、舒适的间距系统
 */

// 基础间距单位（4的倍数，符合设计规范）
const baseUnit = 4;

// 间距规模
export const spacing = {
  // 微小间距
  xxs: baseUnit,        // 4
  xs: baseUnit * 2,     // 8
  
  // 小间距
  sm: baseUnit * 3,     // 12
  
  // 中等间距（默认）
  md: baseUnit * 4,     // 16
  
  // 大间距
  lg: baseUnit * 6,     // 24
  xl: baseUnit * 8,     // 32
  
  // 超大间距
  xxl: baseUnit * 12,   // 48
  xxxl: baseUnit * 16,  // 64
};

// 对话专用间距
export const chatSpacing = {
  // 消息之间的间距
  betweenMessages: spacing.sm,        // 12
  betweenSameUser: spacing.xs,        // 8 (同一用户连续消息)
  
  // 气泡内部间距
  bubblePaddingH: spacing.md,         // 16 (水平)
  bubblePaddingV: spacing.sm,         // 12 (垂直)
  
  // 元信息间距
  senderMargin: spacing.xs,           // 8
  timeStampMargin: baseUnit,          // 4
  statusMargin: spacing.xs,           // 8
  
  // 输入区域
  inputPaddingH: spacing.md,          // 16
  inputPaddingV: spacing.sm,          // 12
  inputMargin: spacing.md,            // 16
};

// 页面内边距
export const screenPadding = {
  horizontal: spacing.md,              // 16
  vertical: spacing.lg,                // 24
  top: spacing.xl,                     // 32 (考虑状态栏)
  bottom: spacing.lg,                  // 24 (考虑底部安全区)
};

// 组件间距
export const componentSpacing = {
  // 按钮
  buttonPaddingH: spacing.lg,         // 24
  buttonPaddingV: spacing.sm,         // 12
  buttonMargin: spacing.md,           // 16
  
  // 卡片
  cardPadding: spacing.md,            // 16
  cardMargin: spacing.md,             // 16
  cardGap: spacing.sm,                // 12
  
  // 列表
  listItemPaddingH: spacing.md,       // 16
  listItemPaddingV: spacing.sm,       // 12
  listItemGap: spacing.xs,            // 8
  
  // 表单
  formFieldMargin: spacing.lg,        // 24
  formLabelMargin: spacing.xs,        // 8
  formHelperMargin: baseUnit,         // 4
};

// 圆角半径
export const borderRadius = {
  // 基础圆角
  xs: baseUnit,                       // 4
  sm: baseUnit * 2,                   // 8
  md: baseUnit * 3,                   // 12
  lg: baseUnit * 4,                   // 16
  xl: baseUnit * 6,                   // 24
  
  // 特殊圆角
  pill: 999,                           // 药丸形
  circle: '50%',                       // 圆形
  
  // 组件专用
  button: baseUnit * 3,               // 12
  card: baseUnit * 4,                 // 16
  bubble: baseUnit * 4,               // 16
  input: baseUnit * 6,                // 24 (输入框)
  modal: baseUnit * 5,                // 20
};

// 图标大小
export const iconSizes = {
  xs: 16,
  sm: 20,
  md: 24,   // 默认
  lg: 28,
  xl: 32,
  xxl: 40,
};

// 头像大小
export const avatarSizes = {
  xs: 24,
  sm: 32,
  md: 40,   // 默认
  lg: 48,
  xl: 56,
  xxl: 64,
};

// 布局尺寸
export const layout = {
  // 最大内容宽度
  maxContentWidth: 600,
  
  // 对话气泡最大宽度（屏幕宽度的百分比）
  maxBubbleWidth: 0.75,
  
  // 最小触摸目标大小（无障碍要求）
  minTouchTarget: 44,
  
  // 输入框高度
  inputHeight: {
    sm: 36,
    md: 44,   // 默认
    lg: 52,
  },
  
  // 按钮高度
  buttonHeight: {
    sm: 32,
    md: 44,   // 默认
    lg: 52,
    xl: 60,
  },
  
  // 导航栏高度
  headerHeight: 56,
  tabBarHeight: 49,
};

// Z-index 层级
export const zIndex = {
  background: -1,
  content: 1,
  elevated: 10,
  sticky: 100,
  dropdown: 200,
  overlay: 300,
  modal: 400,
  popover: 500,
  tooltip: 600,
  toast: 700,
  loading: 800,
};

// 阴影效果
export const shadows = {
  // 极轻微（默认）
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  // 轻微
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  // 中等
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.10,
    shadowRadius: 8,
    elevation: 4,
  },
  // 强烈
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  },
  // 对话气泡专用
  bubble: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
};

// 导出默认配置
export default {
  spacing,
  chatSpacing,
  screenPadding,
  componentSpacing,
  borderRadius,
  iconSizes,
  avatarSizes,
  layout,
  zIndex,
  shadows,
};