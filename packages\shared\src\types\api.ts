/**
 * HER API 类型定义
 * 标准化的API请求和响应格式
 */

import { User, Message, Conversation, Memory, MoodCheckIn, EmotionType } from './models';

// 标准API响应格式
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
  requestId?: string;
}

// API错误响应格式
export interface ApiError {
  error: {
    code: string;
    message: string;
    details?: any;
    timestamp: string;
    requestId: string;
  };
}

// 分页响应格式
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 认证相关接口
export interface AnonymousLoginResponse {
  token: string;
  user: User;
  expiresAt: string;
}

export interface PhoneLoginRequest {
  phone: string;
  code: string;
}

export interface PhoneLoginResponse {
  token: string;
  user: User;
  isNewUser: boolean;
}

// 对话相关接口 - 更新版本
export interface SendMessageRequest {
  conversationId?: string;
  message: string;
  parentMessageId?: string;
  metadata?: {
    timestamp?: string;
    clientId?: string;
    platform?: 'mobile' | 'web';
  };
}

export interface SendMessageResponse {
  success: true;
  data: {
    messageId: string;
    conversationId: string;
    response: {
      id: string;
      content: string;
      role: 'assistant';
      createdAt: string;
      metadata: {
        model: string;
        tokens: number;
        emotion?: string;
      };
    };
  };
}

export interface GetConversationsRequest {
  page?: number;
  limit?: number;
  includeArchived?: boolean;
}

export interface GetMessagesRequest {
  conversationId: string;
  page?: number;
  limit?: number;
  before?: string; // messageId
}

// 记忆相关接口
export interface SearchMemoryRequest {
  query: string;
  limit?: number;
  type?: string;
  minImportance?: number;
}

export interface SaveMemoryRequest {
  type: string;
  content: string;
  importance?: number;
  metadata?: Record<string, any>;
}

// 情绪相关接口
export interface MoodCheckInRequest {
  moodValue: number;
  moodLabel: string;
  note?: string;
  triggers?: string[];
}

export interface GetMoodHistoryRequest {
  days?: number;
  page?: number;
  limit?: number;
}

// 流式响应接口 - 更新版本
export interface StreamChunk {
  type: 'content' | 'metadata' | 'done' | 'error';
  data?: {
    content?: string;
    messageId?: string;
    conversationId?: string;
    tokens?: number;
    model?: string;
    emotion?: EmotionType;
    error?: string;
  };
}

// Server-Sent Events 格式
export interface SSEEvent {
  event?: 'message-start' | 'message-delta' | 'message-stop' | 'error' | 'ping';
  data: string;
  id?: string;
  retry?: number;
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: 'typing' | 'stop_typing' | 'message' | 'emotion_change' | 'error';
  conversationId?: string;
  data?: any;
}