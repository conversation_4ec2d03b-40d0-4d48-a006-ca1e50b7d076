/**
 * HER Themed StyleSheet
 * 提供主题感知的样式创建工具
 */

import { StyleSheet } from 'react-native';
import { useTheme } from './ThemeProvider';
import { designTokens, ThemeContext } from '@her/shared';

// 主题感知的样式创建函数
export const createStyleSheet = <T extends StyleSheet.NamedStyles<T>>(
  styles: T | ((theme: ThemeContext) => T)
): (() => T) => {
  return () => {
    const theme = useTheme();
    
    if (typeof styles === 'function') {
      return StyleSheet.create(styles(theme));
    }
    
    return StyleSheet.create(styles);
  };
};

// 预定义的通用样式
export const createCommonStyles = (theme: ThemeContext) =>
  StyleSheet.create({
    // 容器样式
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    
    safeContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
      paddingTop: designTokens.platformConfig.ios.statusBarHeight,
    },
    
    centeredContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: theme.colors.background,
    },
    
    // 卡片样式
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: designTokens.borderRadius.card,
      padding: designTokens.spacing.md,
      marginBottom: designTokens.spacing.md,
      ...getShadowStyle(designTokens.shadows.sm),
    },
    
    // 文本样式
    heading1: {
      fontSize: designTokens.typography.fontSizes.largeTitle,
      fontWeight: designTokens.typography.fontWeights.bold.toString() as any,
      lineHeight: designTokens.typography.fontSizes.largeTitle * designTokens.typography.lineHeights.tight,
      color: theme.colors.text.primary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    heading2: {
      fontSize: designTokens.typography.fontSizes.title1,
      fontWeight: designTokens.typography.fontWeights.bold.toString() as any,
      lineHeight: designTokens.typography.fontSizes.title1 * designTokens.typography.lineHeights.tight,
      color: theme.colors.text.primary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    heading3: {
      fontSize: designTokens.typography.fontSizes.title2,
      fontWeight: designTokens.typography.fontWeights.semibold.toString() as any,
      lineHeight: designTokens.typography.fontSizes.title2 * designTokens.typography.lineHeights.normal,
      color: theme.colors.text.primary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    bodyText: {
      fontSize: designTokens.typography.fontSizes.body,
      fontWeight: designTokens.typography.fontWeights.regular.toString() as any,
      lineHeight: designTokens.typography.fontSizes.body * designTokens.typography.lineHeights.normal,
      color: theme.colors.text.primary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    secondaryText: {
      fontSize: designTokens.typography.fontSizes.subhead,
      fontWeight: designTokens.typography.fontWeights.regular.toString() as any,
      lineHeight: designTokens.typography.fontSizes.subhead * designTokens.typography.lineHeights.normal,
      color: theme.colors.text.secondary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    captionText: {
      fontSize: designTokens.typography.fontSizes.caption1,
      fontWeight: designTokens.typography.fontWeights.regular.toString() as any,
      lineHeight: designTokens.typography.fontSizes.caption1 * designTokens.typography.lineHeights.normal,
      color: theme.colors.text.secondary,
      fontFamily: designTokens.typography.fontFamilies.chinese,
    },
    
    // 按钮样式
    primaryButton: {
      backgroundColor: theme.colors.accent,
      borderRadius: designTokens.borderRadius.button,
      paddingVertical: designTokens.spacing.md,
      paddingHorizontal: designTokens.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: designTokens.layout.minTouchTarget,
    },
    
    primaryButtonText: {
      fontSize: designTokens.typography.fontSizes.callout,
      fontWeight: designTokens.typography.fontWeights.semibold.toString() as any,
      color: theme.colors.text.primary,
    },
    
    secondaryButton: {
      backgroundColor: 'transparent',
      borderRadius: designTokens.borderRadius.button,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingVertical: designTokens.spacing.md,
      paddingHorizontal: designTokens.spacing.lg,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: designTokens.layout.minTouchTarget,
    },
    
    secondaryButtonText: {
      fontSize: designTokens.typography.fontSizes.callout,
      fontWeight: designTokens.typography.fontWeights.medium.toString() as any,
      color: theme.colors.text.primary,
    },
    
    // 输入框样式
    textInput: {
      backgroundColor: theme.colors.surface,
      borderRadius: designTokens.borderRadius.input,
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: designTokens.spacing.md,
      paddingVertical: designTokens.spacing.sm,
      fontSize: designTokens.typography.fontSizes.body,
      color: theme.colors.text.primary,
      minHeight: 48,
    },
    
    // 分隔线
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginVertical: designTokens.spacing.md,
    },
    
    // 居中内容
    centerContent: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    
    // 行布局
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    
    rowSpaceBetween: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    
    // 间距
    marginTop: {
      marginTop: designTokens.spacing.md,
    },
    
    marginBottom: {
      marginBottom: designTokens.spacing.md,
    },
    
    paddingHorizontal: {
      paddingHorizontal: designTokens.spacing.md,
    },
    
    paddingVertical: {
      paddingVertical: designTokens.spacing.md,
    },
  });

// 辅助函数：转换阴影样式
const getShadowStyle = (shadow: typeof designTokens.shadows.sm) => ({
  shadowColor: shadow.color,
  shadowOffset: {
    width: shadow.offsetX,
    height: shadow.offsetY,
  },
  shadowOpacity: 1,
  shadowRadius: shadow.blur,
  elevation: shadow.offsetY + shadow.blur, // Android 阴影
});

// Hook：获取通用样式
export const useCommonStyles = () => {
  const theme = useTheme();
  return createCommonStyles(theme);
};

// 响应式设计辅助函数
export const responsive = {
  // 根据屏幕宽度返回不同值
  width: (small: any, medium: any, large: any) => {
    const { Dimensions } = require('react-native');
    const { width } = Dimensions.get('window');
    
    if (width < 768) return small;
    if (width < 1024) return medium;
    return large;
  },
  
  // 根据设备类型返回不同值
  device: (phone: any, tablet: any) => {
    const { Dimensions } = require('react-native');
    const { width } = Dimensions.get('window');
    return width < 768 ? phone : tablet;
  },
};