.bmad-core
.claude

# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
/build/
/dist/
*.tgz

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Expo
.expo/
.expo-shared/

# React Native
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/
.metro-health-check*

# CocoaPods
ios/Pods/
ios/Podfile.lock

# Android
android/app/debug
android/app/release
*.keystore
!debug.keystore

# Flipper
ios/Pods/Flipper
ios/Pods/Flipper-Folly
ios/Pods/RCT-Folly

# generated by @expo/next-adapter
.expo/
.vercel

# Sentry
.sentryclirc

# Local Netlify folder
.netlify

# Miscellaneous
*.class
*.lock
*.pyc
*.egg-info/

# Vercel
.vercel

# Claude
.claude.json
CLAUDE.md

# Codebuddy
CODEBUDDY.md