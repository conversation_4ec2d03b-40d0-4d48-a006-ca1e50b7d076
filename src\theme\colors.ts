/**
 * HER Color Theme System
 * 温暖、私密、安全的色彩系统
 */

export interface ColorPalette {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  bubble: {
    her: string;
    user: string;
  };
  accent: string;
  border: string;
  shadow: string;
}

// 基础色彩定义
const baseColors = {
  // 主色调
  morningCream: '#FAF7F2',    // 晨曦米白
  twilightPink: '#F5E6E0',     // 暮光粉
  moonlightBlue: '#E8EEF5',    // 月光蓝
  candleOrange: '#FFE5D4',     // 烛光橙
  
  // 深色调
  midnightBlue: '#2C3E50',     // 午夜蓝
  deepNavy: '#34495E',         // 深海军蓝
  
  // 语义色彩
  gentleGreen: '#95C99F',      // 柔和绿
  softYellow: '#F4C67F',       // 温柔黄
  mildRed: '#E8A5A5',          // 柔和红
  lightBlue: '#A5C4E8',        // 淡蓝
  
  // 中性色
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },
};

// 默认主题（日间）
export const defaultTheme: ColorPalette = {
  primary: baseColors.morningCream,
  secondary: baseColors.twilightPink,
  background: baseColors.morningCream,
  surface: baseColors.white,
  text: {
    primary: baseColors.gray[800],
    secondary: baseColors.gray[600],
    disabled: baseColors.gray[400],
  },
  bubble: {
    her: baseColors.twilightPink,
    user: baseColors.moonlightBlue,
  },
  accent: baseColors.candleOrange,
  border: `${baseColors.gray[200]}40`, // 40% opacity
  shadow: 'rgba(0, 0, 0, 0.08)',
};

// 宁静模式
export const peacefulTheme: ColorPalette = {
  ...defaultTheme,
  primary: baseColors.moonlightBlue,
  background: '#F0F4F8',
  bubble: {
    her: '#E0E8F5',
    user: '#D4E2F5',
  },
  accent: baseColors.lightBlue,
};

// 温暖模式
export const warmTheme: ColorPalette = {
  ...defaultTheme,
  primary: baseColors.candleOrange,
  background: '#FFF9F5',
  bubble: {
    her: baseColors.candleOrange,
    user: baseColors.twilightPink,
  },
  accent: '#FFA07A',
};

// 深夜模式
export const nightTheme: ColorPalette = {
  primary: baseColors.midnightBlue,
  secondary: baseColors.deepNavy,
  background: baseColors.gray[900],
  surface: baseColors.gray[800],
  text: {
    primary: baseColors.gray[100],
    secondary: baseColors.gray[300],
    disabled: baseColors.gray[500],
  },
  bubble: {
    her: baseColors.deepNavy,
    user: '#3A506B',
  },
  accent: '#5A7A9A',
  border: `${baseColors.gray[700]}60`,
  shadow: 'rgba(0, 0, 0, 0.3)',
};

// 时间感知主题选择器
export const getTimeBasedTheme = (): ColorPalette => {
  const hour = new Date().getHours();
  
  if (hour >= 6 && hour < 12) {
    return defaultTheme; // 早晨
  } else if (hour >= 12 && hour < 18) {
    return peacefulTheme; // 下午
  } else if (hour >= 18 && hour < 22) {
    return warmTheme; // 傍晚
  } else {
    return nightTheme; // 深夜
  }
};

// 情绪适应色彩
export const emotionalColors = {
  listening: {
    primary: '#E8F4F8',
    accent: '#7FC4D6',
    bubble: '#D6E9F0',
  },
  thinking: {
    primary: '#F8F4E8',
    accent: '#D6C47F',
    bubble: '#F0E9D6',
  },
  caring: {
    primary: '#F8E8F4',
    accent: '#D67FA6',
    bubble: '#F0D6E9',
  },
  understanding: {
    primary: '#E8F8E8',
    accent: '#7FD67F',
    bubble: '#D6F0D6',
  },
};

// 透明度助手
export const withOpacity = (color: string, opacity: number): string => {
  const hex = Math.round(opacity * 255).toString(16).padStart(2, '0');
  return `${color}${hex}`;
};

// 渐变色定义
export const gradients = {
  warmSunrise: ['#FFE5D4', '#F5E6E0'],
  coolEvening: ['#E8EEF5', '#E0E8F5'],
  nightfall: ['#34495E', '#2C3E50'],
  peaceful: ['#D4E2F5', '#E0E8F5'],
  gentle: ['#F5E6E0', '#FAF7F2'],
};

// 导出所有主题
export const themes = {
  default: defaultTheme,
  peaceful: peacefulTheme,
  warm: warmTheme,
  night: nightTheme,
};

// 导出基础色彩供其他组件使用
export { baseColors };

export default defaultTheme;