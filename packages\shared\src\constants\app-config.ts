/**
 * HER 应用配置常量
 */

import { FeatureFlags } from '../types/common';

// API配置
export const API_CONFIG = {
  TIMEOUT: 10000, // 10秒超时
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1秒重试延迟
  STREAM_TIMEOUT: 30000, // 30秒流式超时
} as const;

// 消息限制
export const MESSAGE_LIMITS = {
  MAX_LENGTH: 2000,
  MIN_LENGTH: 1,
  MAX_HISTORY: 100,
  TYPING_DEBOUNCE: 1000, // 1秒输入防抖
} as const;

// 记忆系统配置
export const MEMORY_CONFIG = {
  EMBEDDING_DIMENSION: 1536,
  MAX_SEARCH_RESULTS: 10,
  MIN_IMPORTANCE: 0.3,
  MAX_MEMORIES_PER_USER: 10000,
  CLEANUP_THRESHOLD: 0.1, // 清理重要性阈值
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  CONVERSATIONS_TTL: 30 * 60 * 1000, // 30分钟
  MESSAGES_TTL: 60 * 60 * 1000, // 1小时
  USER_PROFILE_TTL: 15 * 60 * 1000, // 15分钟
  MAX_CACHE_SIZE: 100, // 最大缓存条目数
} as const;

// 情绪系统配置
export const EMOTION_CONFIG = {
  DEFAULT_EMOTION: 'neutral',
  EMOTION_CHANGE_COOLDOWN: 5000, // 5秒情绪变化冷却
  MOOD_SCALE_MIN: 0,
  MOOD_SCALE_MAX: 100,
} as const;

// 安全配置
export const SECURITY_CONFIG = {
  JWT_EXPIRY: '30d',
  REFRESH_THRESHOLD: 7 * 24 * 60 * 60 * 1000, // 7天
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15分钟
  ENCRYPTION_KEY_LENGTH: 32,
} as const;

// 通知配置
export const NOTIFICATION_CONFIG = {
  QUIET_HOURS_START: 22, // 晚上10点
  QUIET_HOURS_END: 8, // 早上8点
  MAX_DAILY_NOTIFICATIONS: 10,
  REMINDER_INTERVALS: [1, 3, 7, 14], // 天数
} as const;

// 默认功能开关
export const DEFAULT_FEATURE_FLAGS: FeatureFlags = {
  voiceChat: false, // 语音对话（开发中）
  memorySearch: true, // 记忆搜索
  moodTracking: true, // 情绪跟踪
  notifications: true, // 通知推送
  analytics: true, // 数据分析
  debugMode: false, // 调试模式
} as const;

// 应用元信息
export const APP_INFO = {
  NAME: 'HER',
  DESCRIPTION: '私密AI陪伴应用',
  VERSION: '1.0.0',
  BUILD: '1',
  AUTHOR: 'HER Team',
  WEBSITE: 'https://her-app.com',
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

// 存储键名（重新导出以保持向后兼容）
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  CONVERSATIONS: 'conversations_cache',
  THEME: 'selected_theme',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_SYNC: 'last_sync_time',
} as const;