/**
 * JWT认证中间件
 * 使用方法：withAuth(handler)包装需要认证的API函数
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import jwt from 'jsonwebtoken';

export type AuthenticatedRequest = VercelRequest & {
  userId: string;
  user: {
    id: string;
    isAnonymous: boolean;
  };
};

export type APIHandler = (
  req: AuthenticatedRequest,
  res: VercelResponse
) => Promise<void | VercelResponse>;

/**
 * JWT认证中间件包装器
 */
export function withAuth(handler: APIHandler) {
  return async (req: VercelRequest, res: VercelResponse) => {
    // 预检请求直接通过
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          code: 'AUTH_MISSING',
          message: '缺少认证令牌',
          timestamp: new Date().toISOString(),
        }
      });
    }

    const token = authHeader.replace('Bearer ', '');

    try {
      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
      
      if (!decoded.sub || !decoded.userId) {
        throw new Error('Invalid token payload');
      }

      // 添加用户信息到请求对象
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.userId = decoded.userId || decoded.sub;
      authenticatedReq.user = {
        id: decoded.userId || decoded.sub,
        isAnonymous: decoded.isAnonymous || false,
      };

      // 调用原始处理器
      return handler(authenticatedReq, res);
      
    } catch (error: any) {
      console.error('JWT验证失败:', error.message);
      
      return res.status(401).json({
        error: {
          code: 'AUTH_INVALID',
          message: '认证令牌无效或已过期',
          timestamp: new Date().toISOString(),
        }
      });
    }
  };
}

/**
 * 可选认证中间件（支持匿名访问）
 */
export function withOptionalAuth(handler: APIHandler) {
  return async (req: VercelRequest, res: VercelResponse) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // 无认证时，设置匿名用户
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.userId = 'anonymous';
      authenticatedReq.user = {
        id: 'anonymous',
        isAnonymous: true,
      };
      return handler(authenticatedReq, res);
    }

    // 有认证时，走正常认证流程
    return withAuth(handler)(req, res);
  };
}

/**
 * 生成JWT token
 */
export function generateToken(payload: {
  userId: string;
  isAnonymous?: boolean;
  expiresIn?: string;
}) {
  return jwt.sign(
    {
      sub: payload.userId,
      userId: payload.userId,
      isAnonymous: payload.isAnonymous || false,
      iat: Math.floor(Date.now() / 1000),
    },
    process.env.JWT_SECRET || 'fallback-secret',
    {
      expiresIn: payload.expiresIn || '30d', // 30天过期
    }
  );
}

/**
 * 验证JWT token（不通过中间件）
 */
export function verifyToken(token: string): {
  userId: string;
  isAnonymous: boolean;
} | null {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
    
    return {
      userId: decoded.userId || decoded.sub,
      isAnonymous: decoded.isAnonymous || false,
    };
  } catch (error) {
    return null;
  }
}