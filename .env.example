# HER Development Environment Variables
# 复制此文件为 .env.local 并填入实际值

# ============================================
# Supabase Configuration (数据库配置)
# ============================================
# 从 https://supabase.com/dashboard 获取
NEXT_PUBLIC_SUPABASE_URL=your-project-url.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# ============================================
# AI Configuration (AI配置)
# ============================================
# 选择 AI 提供商: 'openai' 或 'openrouter'
AI_PROVIDER=openrouter

# OpenRouter 配置 (推荐)
# 从 https://openrouter.ai/keys 获取
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-key
OPENROUTER_MODEL=openai/gpt-4-turbo-preview
# 可选模型:
# - openai/gpt-3.5-turbo (便宜)
# - anthropic/claude-3-haiku (快速)
# - meta-llama/llama-3-70b-instruct (开源)
# - mistralai/mixtral-8x7b-instruct (性价比)

# OpenAI 直连配置 (备选)
# 从 https://platform.openai.com/api-keys 获取
OPENAI_API_KEY=sk-your-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_EMBEDDING_MODEL=text-embedding-3-small

# ============================================
# Vercel Configuration (部署配置)
# ============================================
# Vercel 会自动设置这些
VERCEL_URL=
VERCEL_ENV=development

# ============================================
# Application Settings (应用设置)
# ============================================
# 应用基础配置
NEXT_PUBLIC_APP_NAME=HER
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# 安全配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key

# ============================================
# Cloudflare (CDN/安全配置) - 可选
# ============================================
CLOUDFLARE_ACCOUNT_ID=
CLOUDFLARE_API_TOKEN=
CLOUDFLARE_ZONE_ID=

# ============================================
# Analytics (分析配置) - 可选
# ============================================
NEXT_PUBLIC_POSTHOG_KEY=
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# ============================================
# Mobile App Configuration (移动端配置)
# ============================================
# Expo 配置
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_SUPABASE_URL=your-project-url.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# ============================================
# Development Settings (开发设置)
# ============================================
NODE_ENV=development
LOG_LEVEL=debug
ENABLE_DEBUG_MODE=true