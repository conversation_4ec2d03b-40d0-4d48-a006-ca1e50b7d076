/**
 * CORS (跨域资源共享) 中间件
 * 使用方法：withCors(handler)包装需要CORS支持的API函数
 */

import { VercelRequest, VercelResponse } from '@vercel/node';

export type CORSOptions = {
  origin?: string | string[] | boolean;
  methods?: string[];
  allowedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
};

const DEFAULT_CORS_OPTIONS: Required<CORSOptions> = {
  origin: true, // 允许所有源，生产环境建议限制
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-HTTP-Method-Override',
  ],
  credentials: true,
  maxAge: 86400, // 24小时
};

/**
 * CORS中间件包装器
 */
export function withCors<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => R,
  options: CORSOptions = {}
) {
  const corsOptions = { ...DEFAULT_CORS_OPTIONS, ...options };

  return async (req: VercelRequest, res: VercelResponse, ...args: T): Promise<R> => {
    // 设置CORS头
    setCorsHeaders(req, res, corsOptions);

    // 处理预检请求
    if (req.method === 'OPTIONS') {
      return res.status(200).end() as any;
    }

    // 调用原始处理器
    return handler(req, res, ...args);
  };
}

/**
 * 设置CORS响应头
 */
function setCorsHeaders(
  req: VercelRequest,
  res: VercelResponse,
  options: Required<CORSOptions>
) {
  const { origin, methods, allowedHeaders, credentials, maxAge } = options;

  // 设置 Access-Control-Allow-Origin
  if (typeof origin === 'boolean') {
    if (origin) {
      res.setHeader('Access-Control-Allow-Origin', '*');
    }
  } else if (typeof origin === 'string') {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else if (Array.isArray(origin)) {
    const requestOrigin = req.headers.origin;
    if (requestOrigin && origin.includes(requestOrigin)) {
      res.setHeader('Access-Control-Allow-Origin', requestOrigin);
    }
  }

  // 设置其他CORS头
  res.setHeader('Access-Control-Allow-Methods', methods.join(', '));
  res.setHeader('Access-Control-Allow-Headers', allowedHeaders.join(', '));
  res.setHeader('Access-Control-Max-Age', maxAge.toString());

  if (credentials) {
    res.setHeader('Access-Control-Allow-Credentials', 'true');
  }

  // 设置Vary头以支持缓存
  res.setHeader('Vary', 'Origin, Access-Control-Request-Method, Access-Control-Request-Headers');
}

/**
 * HER应用专用CORS配置
 */
export const HER_CORS_OPTIONS: CORSOptions = {
  origin: [
    'http://localhost:3000',      // 本地开发
    'http://localhost:8081',      // Expo开发服务器
    'https://her-app.com',        // 生产域名
    'https://staging.her-app.com', // 测试域名
    'https://preview.her-app.com', // 预览域名
  ],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
  ],
};

/**
 * HER应用CORS中间件
 */
export function withHerCors<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => R
) {
  return withCors(handler, HER_CORS_OPTIONS);
}

/**
 * 开发环境CORS中间件（允许所有源）
 */
export function withDevCors<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => R
) {
  const devOptions: CORSOptions = {
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD'],
    allowedHeaders: ['*'],
  };

  return withCors(handler, devOptions);
}

/**
 * 检查是否为预检请求
 */
export function isPreflightRequest(req: VercelRequest): boolean {
  return req.method === 'OPTIONS' && !!req.headers['access-control-request-method'];
}

/**
 * 检查请求源是否被允许
 */
export function isOriginAllowed(
  requestOrigin: string | undefined,
  allowedOrigins: string | string[] | boolean
): boolean {
  if (allowedOrigins === true) return true;
  if (allowedOrigins === false) return false;
  if (!requestOrigin) return false;

  if (typeof allowedOrigins === 'string') {
    return requestOrigin === allowedOrigins;
  }

  if (Array.isArray(allowedOrigins)) {
    return allowedOrigins.includes(requestOrigin);
  }

  return false;
}