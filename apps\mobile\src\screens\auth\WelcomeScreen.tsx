/**
 * HER Welcome Screen
 * 欢迎页面 - 认证流程入口
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '@her/ui';
import { useAuthStore } from '../../stores/authStore';
import { AuthStackParamList } from '../../navigation/AuthNavigator';

type WelcomeScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'Welcome'>;

interface Props {
  navigation: WelcomeScreenNavigationProp;
}

export const WelcomeScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();
  const { loginAnonymously } = useAuthStore();

  const handleAnonymousLogin = async () => {
    try {
      await loginAnonymously();
    } catch (error) {
      console.error('Anonymous login failed:', error);
      // 这里可以显示错误提示
    }
  };

  const handlePhoneLogin = () => {
    navigation.navigate('PhoneLogin');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Logo Section */}
        <View style={styles.logoSection}>
          <View style={[styles.logo, { backgroundColor: theme.colors.accent }]}>
            <Text style={[styles.logoText, { color: theme.colors.text.primary }]}>
              HER
            </Text>
          </View>
          
          <Text style={[styles.title, { color: theme.colors.text.primary }]}>
            欢迎来到 HER
          </Text>
          
          <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
            你的私密AI陪伴，随时倾听你的心声
          </Text>
        </View>

        {/* Features */}
        <View style={styles.features}>
          {[
            { emoji: '🌸', title: '温暖陪伴', desc: '24小时贴心陪伴' },
            { emoji: '🔒', title: '绝对私密', desc: '端到端加密保护' },
            { emoji: '💝', title: '情感理解', desc: '懂你的喜怒哀乐' },
          ].map((feature, index) => (
            <View key={index} style={styles.feature}>
              <Text style={styles.featureEmoji}>{feature.emoji}</Text>
              <View style={styles.featureText}>
                <Text style={[styles.featureTitle, { color: theme.colors.text.primary }]}>
                  {feature.title}
                </Text>
                <Text style={[styles.featureDesc, { color: theme.colors.text.secondary }]}>
                  {feature.desc}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Actions */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={[
            styles.primaryButton,
            { backgroundColor: theme.colors.accent },
          ]}
          onPress={handleAnonymousLogin}
        >
          <Text style={[styles.primaryButtonText, { color: theme.colors.text.primary }]}>
            开始匿名体验
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.secondaryButton,
            { borderColor: theme.colors.border },
          ]}
          onPress={handlePhoneLogin}
        >
          <Text style={[styles.secondaryButtonText, { color: theme.colors.text.primary }]}>
            手机号登录
          </Text>
        </TouchableOpacity>

        <Text style={[styles.disclaimer, { color: theme.colors.text.secondary }]}>
          继续使用即表示同意服务条款和隐私政策
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60, // 状态栏高度
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  logoSection: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  logoText: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 32,
  },
  features: {
    flex: 1,
    justifyContent: 'center',
    paddingVertical: 32,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  featureEmoji: {
    fontSize: 32,
    marginRight: 16,
  },
  featureText: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  featureDesc: {
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    paddingHorizontal: 24,
    paddingBottom: 34, // 安全区域
  },
  primaryButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    marginBottom: 16,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  disclaimer: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
});