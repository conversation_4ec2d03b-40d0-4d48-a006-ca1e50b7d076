/**
 * API Utilities
 * 通用工具函数
 */

import { ApiResponse, ApiError } from '@her/shared';

// 创建标准化API响应
export const apiResponse = <T>(
  data: T,
  success = true,
  message?: string,
  requestId?: string
): ApiResponse<T> => {
  return {
    data,
    success,
    message,
    timestamp: new Date().toISOString(),
    requestId,
  };
};

// 创建错误响应
export const apiError = (
  code: string,
  message: string,
  details?: any,
  requestId?: string
): ApiError => {
  return {
    error: {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      requestId: requestId || generateRequestId(),
    },
  };
};

// 生成请求ID
export const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 提取请求ID
export const getRequestId = (req: any): string => {
  return req.headers['x-request-id'] || generateRequestId();
};

// CORS处理
export const setCorsHeaders = (res: any): void => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader(
    'Access-Control-Allow-Methods',
    'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS'
  );
  res.setHeader(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, X-Request-ID'
  );
};

// 处理OPTIONS请求
export const handleCors = (req: any, res: any): boolean => {
  setCorsHeaders(res);
  
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return true;
  }
  
  return false;
};

// 日志记录
export const logRequest = (req: any, startTime: number): void => {
  const duration = Date.now() - startTime;
  console.log(`${req.method} ${req.url} - ${duration}ms`);
};

// 错误日志记录
export const logError = (error: any, req: any, requestId: string): void => {
  console.error('API Error:', {
    requestId,
    method: req.method,
    url: req.url,
    error: error.message,
    stack: error.stack,
  });
};

// 验证必需的环境变量
export const validateEnvVars = (requiredVars: string[]): void => {
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
};

// 解析JSON请求体
export const parseJsonBody = (req: any): any => {
  if (typeof req.body === 'string') {
    try {
      return JSON.parse(req.body);
    } catch {
      throw new Error('Invalid JSON body');
    }
  }
  
  return req.body;
};

// 清理敏感信息（用于日志）
export const sanitizeForLog = (obj: any): any => {
  const sanitized = { ...obj };
  
  // 移除敏感字段
  const sensitiveFields = ['password', 'token', 'secret', 'key'];
  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });
  
  return sanitized;
};