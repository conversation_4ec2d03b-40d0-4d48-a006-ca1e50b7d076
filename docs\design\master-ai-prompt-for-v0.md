# HER - Master AI Frontend Generation Prompt

> 🎯 **使用说明**: 这是为v0.dev或Lovable.dev准备的完整提示词。直接复制下方的提示词内容，粘贴到AI工具中即可生成HER的前端界面。

## 🚀 快速使用步骤

1. **选择AI工具**:
   - 访问 [v0.dev](https://v0.dev) (Vercel的AI代码生成器)
   - 或访问 [Lovable.dev](https://lovable.dev) (全栈AI开发工具)

2. **复制提示词**:
   - 复制下方 "Complete Prompt" 部分的全部内容

3. **生成代码**:
   - 粘贴到AI工具的输入框
   - 点击生成，等待10-30秒
   - 预览生成的界面

4. **迭代优化**:
   - 如果需要调整，可以继续对话
   - 例如: "把背景色调整得更温暖一些"
   - 或: "添加一个语音输入按钮"

---

## 📝 Complete Prompt for AI Generation

```prompt
# High-Level Goal
Create a warm, emotionally intelligent chat interface for HER, a private AI companion mobile app. The interface should feel like a "warm digital bedroom" - safe, private, and comforting. Build with React Native + TypeScript, focusing on emotional design with breathing animations and time-aware themes.

# Technical Stack & Requirements
- **Framework**: React Native 0.73+ with TypeScript 5
- **State Management**: Zustand
- **UI Components**: Custom components (no external UI libraries)
- **Styling**: Inline styles with predefined theme system
- **Storage**: AsyncStorage for message persistence
- **Target Platforms**: iOS and Android
- **Orientation**: Portrait only
- **Language**: UI text in Simplified Chinese

# Core Design System Implementation

## Color Palette (USE THESE EXACT VALUES)
```typescript
const colors = {
  // Primary Palette
  morningCream: '#FAF7F2',    // Main background
  twilightPink: '#F5E6E0',     // HER messages background
  moonlightBlue: '#E8EEF5',    // User messages background
  candleOrange: '#FFE5D4',     // Accent, warm highlights
  
  // Text Colors
  textPrimary: '#262626',      // Main text
  textSecondary: '#525252',    // Secondary text
  textHint: '#A5A5A5',         // Placeholder text
  
  // Semantic Colors
  successGreen: '#95C99F',     // Soft green for positive
  warningYellow: '#F4C67F',    // Gentle yellow for warnings
  errorRed: '#E8A5A5',         // Soft red for errors
  infoBlue: '#A5C4E8',         // Light blue for info
  
  // Dark Mode (Night theme)
  nightBackground: '#2C3E50',
  nightCard: '#34495E',
  nightText: '#E8E8E8',
};
```

## Typography System
```typescript
const typography = {
  fonts: {
    chinese: 'PingFangSC-Regular',  // iOS Chinese
    english: 'Inter-Regular',        // English text
    chat: 'Nunito-Regular',          // Chat messages (rounded)
  },
  sizes: {
    largeTitle: 28,
    title: 24,
    body: 16,         // Default message size
    callout: 15,
    footnote: 13,
    caption: 11,      // Timestamps
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,     // For chat messages
  },
};
```

## Spacing Grid (4px base)
```typescript
const spacing = {
  xxs: 4,
  xs: 8,
  sm: 12,
  md: 16,    // Base unit
  lg: 24,
  xl: 32,
  xxl: 48,
};
```

# Step-by-Step Component Instructions

## 1. Main Chat Screen (`ChatScreen.tsx`)
Create the primary chat interface with these specifications:
- Use SafeAreaView with morningCream (#FAF7F2) background
- Simple header: centered "HER" text (24px, no back button)
- Add KeyboardAvoidingView for iOS keyboard handling
- Implement subtle breathing animation on the header (scale 1.0 to 1.02, 4s loop)
- Structure: Header -> MessageList -> MessageInput

## 2. Message List Component (`MessageList.tsx`)
Build the scrollable message container:
- Use FlatList with inverted={true} for chat behavior
- Group consecutive messages from same sender (8px gap between grouped)
- 12px gap between different sender message groups
- Add contentContainerStyle padding: top 48px, bottom 16px
- Implement pull-to-refresh WITHOUT visible spinner (use subtle bounce)
- Auto-scroll to bottom on new messages
- Performance: Use getItemLayout and keyExtractor

## 3. Chat Bubble Component (`ChatBubble.tsx`)
Design message bubbles with emotional warmth:

**HER Messages (left-aligned)**:
- Background: twilightPink (#F5E6E0)
- Max width: 75% of screen width
- Border radius: 16px top-right, bottom-right, bottom-left
- Top-left radius: 8px for consecutive messages, 16px for first
- Padding: 12px horizontal, 10px vertical
- Text: 16px, textPrimary color, 1.8 line height

**User Messages (right-aligned)**:
- Background: moonlightBlue (#E8EEF5)
- Max width: 75% of screen width
- Border radius: 16px top-left, bottom-left, bottom-right
- Top-right radius: 8px for consecutive, 16px for first
- Same padding and text specs as HER messages

**Common Features**:
- Shadow: { shadowColor: '#000', shadowOpacity: 0.08, shadowRadius: 4 }
- Entry animation: Fade in (opacity 0→1) + slide up (translateY 10→0) over 300ms
- Long press: Show timestamp below message (11px, textSecondary)

## 4. Emotional Loader Component (`EmotionalLoader.tsx`)
Create three distinct loading states with animations:

```typescript
interface LoaderState {
  listening: "正在倾听...";   // 3 expanding ripple circles
  thinking: "思考中...";       // Breathing glow effect
  understanding: "理解中...";  // Gentle pulse animation
}
```

Implementation:
- Container: twilightPink bubble, left-aligned like HER message
- Animation area: 60px height
- State text: 14px below animation, textSecondary color

**Listening Animation**: 
- 3 circles (8px each), horizontal layout
- Staggered scale animation (0.8→1.2→0.8)
- 1.5s duration, 0.2s delay between circles

**Thinking Animation**:
- Single 40px circle with gradient
- Opacity animation (0.4→0.8→0.4)
- 2s duration, ease-in-out

**Understanding Animation**:
- Heart-like shape, 36px
- Scale animation (0.9→1.1→0.9) 
- 1.8s duration, with slight rotation

## 5. Message Input Component (`MessageInput.tsx`)
Build the input area with voice support:

**Container**:
- Fixed bottom position with safe area padding
- White background, top border: 1px #F0F0F0
- Padding: 12px horizontal, 8px vertical

**Input Field**:
- Pill-shaped container (borderRadius: 24px)
- Background: white, border: 1px #E8E8E8
- Flexbox row: [VoiceButton] [TextInput] [SendButton]
- Height: starts at 44px, auto-grows to max 100px (4 lines)

**TextInput**:
- Placeholder: "想说些什么..." (color: #A5A5A5)
- Font: 16px Nunito
- Padding: 12px (accounting for buttons)
- multiline={true}, scrollEnabled when >4 lines

**Voice Button** (left):
- 32px circle, transparent background
- Microphone icon (20px, #525252)
- Touch: scale to 0.9 with haptic feedback
- Show pulse animation when recording

**Send Button** (right):
- 32px circle, candleOrange background when has text
- Paper plane icon (20px, white when active)
- Disabled state: gray background when empty
- Touch: scale to 0.95, rotate icon 45deg on send

## 6. Empty State Component (`EmptyState.tsx`)
Welcome screen when no messages:

**Breathing Circle**:
- 80px diameter, twilightPink background
- Center of screen (flex: 1, justifyContent: center)
- Continuous scale animation (1.0→1.1→1.0, 3s)
- Soft shadow for depth

**Time-Based Greeting** (24px below circle):
```javascript
const greetings = {
  morning: "早安，今天想聊些什么吗？",    // 6:00-12:00
  afternoon: "午后时光，需要休息一下吗？",  // 12:00-18:00
  evening: "晚上好，今天辛苦了",          // 18:00-22:00
  night: "夜深了，睡不着吗？",            // 22:00-6:00
};
```

**Conversation Starters** (40px below greeting):
- 4 pills in 2x2 grid, 12px gap
- Pill style: candleOrange border, transparent fill
- Text + emoji: 14px
- Options:
  - "今天的心情 💭"
  - "有些事想说 ✨"
  - "就是想聊聊 💫"
  - "随便看看 ☕"
- Touch: fill with candleOrange, white text

## 7. Mood Check-In Widget (`MoodCheckIn.tsx`)
Optional mood tracker that slides up:

**Container**:
- Absolute position, bottom: 100px
- White card with 20px border radius
- Padding: 20px, margin: 16px horizontal
- Slide up animation on appear (300ms)

**Prompt Text**:
- "今天感觉怎么样？" (16px, center)
- Below: visual mood slider

**Mood Slider**:
- Width: 100%, height: 40px
- Gradient track: #E8A5A5 (sad) → #FFE5D4 (neutral) → #95C99F (happy)
- Emoji at ends: 😔 ← → 😊 (20px)
- Thumb: 28px white circle with shadow
- No numeric values shown

**Actions**:
- "Skip" text button: "今天不想说" (right corner, 14px, textSecondary)
- Confirm: auto-submit 1s after release

## 8. Time-Aware Theme System
Implement automatic theme switching:

```javascript
const getTimeTheme = () => {
  const hour = new Date().getHours();
  
  if (hour >= 6 && hour < 12) return 'morning';
  if (hour >= 12 && hour < 18) return 'afternoon';  
  if (hour >= 18 && hour < 22) return 'evening';
  return 'night';
};

const themes = {
  morning: {
    background: '#FAF7F2',
    herBubble: '#F5E6E0',
    accent: '#FFE5D4',
  },
  evening: {
    background: '#F5E6E0',
    herBubble: '#FFE5D4',
    accent: '#E8EEF5',
  },
  night: {
    background: '#34495E',
    herBubble: '#2C3E50',
    accent: '#4A5F7A',
    textColor: '#E8E8E8',
  },
};
```

Transition: 2s fade when theme changes

## 9. Micro-Interactions & Haptics

**Touch Feedback**:
- All buttons: scale to 0.98 for 100ms
- Use Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)

**Message Send Animation**:
- Input text fades out
- Message appears with bubble animation
- Slight bounce at end (overshoot by 5%)

**Typing Indicator**:
- Show "HER正在输入..." in input area
- Match actual typing rhythm (not constant)

**Pull Gestures**:
- Elastic stretch on over-scroll
- No loading spinner, just stretch feedback

## 10. Performance Optimizations

**Required Optimizations**:
- Use React.memo for all components
- Implement FlatList with getItemLayout
- Image lazy loading with fade-in
- Limit message history to 100 items in state
- Use InteractionManager for animations
- Enable Hermes on Android

**State Management (Zustand)**:
```typescript
interface ChatStore {
  messages: Message[];
  isHerTyping: boolean;
  userMood?: number;
  addMessage: (message: Message) => void;
  setTyping: (typing: boolean) => void;
}
```

# Constraints & Important Notes

## DO NOT Include:
- User avatars or profile pictures
- Read receipts or "seen" indicators  
- Timestamps (unless long-pressed)
- Loading spinners (use gentle animations instead)
- Harsh colors or sharp corners
- Bottom tab navigation
- Login/signup screens
- Settings pages
- Network error modals (use inline messages)

## MUST Have:
- 60fps smooth animations (useNativeDriver: true)
- Haptic feedback on all interactions
- Message persistence in AsyncStorage
- Keyboard avoiding behavior
- Safe area handling for notches
- Accessibility labels in Chinese
- Memory efficient FlatList

## Mobile Responsive Specs:
- Primary: iPhone 14 Pro (390x844)
- Android: Handle various densities
- Tablet: Scale spacing by 1.5x, cap message width at 600px
- Landscape: Not supported, lock to portrait

# Testing Data

Use this mock data for initial render:

```typescript
const mockMessages = [
  {
    id: '1',
    text: '晚上好呀，今天过得怎么样？',
    sender: 'her',
    timestamp: Date.now() - 3600000,
  },
  {
    id: '2',
    text: '有点累，但还好。就是想找个人说说话',
    sender: 'user',
    timestamp: Date.now() - 3000000,
  },
  {
    id: '3',
    text: '我在这里陪着你呢。累了就是要好好休息，有什么想说的，我都会认真听的',
    sender: 'her',
    timestamp: Date.now() - 2400000,
  },
];
```

# Final Implementation Notes

1. Start with the ChatScreen as the root component
2. Build components in isolation, then integrate
3. Test on both iOS and Android simulators
4. Verify 60fps performance with React DevTools
5. Ensure Chinese text renders correctly
6. Add error boundaries for production stability

Remember: This is not just a chat interface. It's a safe space for emotional expression. Every pixel should convey warmth, understanding, and absolute privacy. The user should feel like they're talking to a trusted friend in a cozy, private room.
```

---

## 🎯 额外的组件级提示词

如果你想要分步生成各个组件，可以使用以下更精细的提示词：

### 单独生成聊天气泡组件
```prompt
Create a React Native ChatBubble component with TypeScript. 
- Two types: 'her' (left, #F5E6E0 background) and 'user' (right, #E8EEF5 background)
- Max width 75%, border radius 16px (8px for consecutive same-sender)
- Fade in + slide up animation (300ms)
- Font: 16px Nunito, line height 1.8
- Subtle shadow (opacity 0.08)
- Long press shows timestamp
```

### 单独生成输入框组件
```prompt
Create a React Native MessageInput component.
- Pill-shaped (24px radius), white background
- Auto-grow from 44px to max 100px (4 lines)
- Voice button left, send button right (32px circles)
- Placeholder: "想说些什么..." in #A5A5A5
- Send button activates with candleOrange (#FFE5D4) when has text
- Include haptic feedback on button presses
```

### 单独生成加载动画
```prompt
Create an EmotionalLoader component for React Native.
- Three states: listening (ripples), thinking (breathing glow), understanding (pulse)
- Container: #F5E6E0 background, left-aligned like a message
- State text below: "正在倾听..." / "思考中..." / "理解中..."
- Smooth, calming animations using Animated API
- 60fps performance with useNativeDriver
```

---

## 💡 使用技巧

1. **第一次生成**: 使用完整的 Master Prompt，让AI生成整体框架
2. **优化迭代**: 使用组件级提示词微调特定部分
3. **调试问题**: 告诉AI具体的错误信息，它会帮你修复
4. **样式调整**: 描述你想要的视觉效果，如"更柔和"、"更有呼吸感"
5. **性能优化**: 要求AI添加"React.memo"和"useCallback"优化

## ⚠️ 重要提醒

- **AI生成的代码需要人工审查** - 特别是安全性和性能方面
- **测试覆盖** - 在真机上测试动画流畅度
- **隐私检查** - 确保没有敏感信息泄露
- **无障碍** - 验证屏幕阅读器兼容性

---

**记住核心理念**: HER不仅仅是一个聊天应用，而是用户最私密、最安全的心灵避风港。每一个设计细节都应该传达温暖、理解和绝对的安全感。

_Generated with love for HER Project_ 💝