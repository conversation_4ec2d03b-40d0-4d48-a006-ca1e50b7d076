/**
 * HER API 工具函数
 * 提供API请求的通用处理和工具函数
 */

import { ApiResponse, ApiError, PaginatedResponse, StreamChunk } from '../types/api';
import { HerError } from '../types/common';

// 构建查询字符串
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)));
      } else {
        searchParams.append(key, String(value));
      }
    }
  });
  
  return searchParams.toString();
};

// 构建完整的URL
export const buildApiUrl = (baseUrl: string, endpoint: string, params?: Record<string, any>): string => {
  const url = new URL(endpoint, baseUrl);
  
  if (params) {
    const queryString = buildQueryString(params);
    if (queryString) {
      url.search = queryString;
    }
  }
  
  return url.toString();
};

// 解析API响应
export const parseApiResponse = <T>(response: any): ApiResponse<T> => {
  if (response.data && typeof response.data === 'object' && 'success' in response.data) {
    return response.data as ApiResponse<T>;
  }
  
  // 兼容直接返回数据的情况
  return {
    data: response.data,
    success: true,
    timestamp: new Date().toISOString(),
  };
};

// 解析API错误
export const parseApiError = (error: any): HerError => {
  if (error.response?.data?.error) {
    const apiError = error.response.data.error as ApiError['error'];
    return new HerError(
      apiError.message,
      apiError.code,
      error.response.status,
      apiError.details
    );
  }
  
  if (error.code === 'ECONNABORTED') {
    return new HerError('请求超时', 'TIMEOUT', 408);
  }
  
  if (error.code === 'NETWORK_ERROR') {
    return new HerError('网络连接失败', 'NETWORK_ERROR', 0);
  }
  
  return new HerError(
    error.message || '未知错误',
    'UNKNOWN_ERROR',
    error.response?.status
  );
};

// 重试逻辑
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      // 不重试的错误类型
      if (error instanceof HerError) {
        const nonRetryableCodes = ['AUTH_INVALID_TOKEN', 'AUTH_UNAUTHORIZED', 'VALIDATION_ERROR'];
        if (nonRetryableCodes.includes(error.code)) {
          throw error;
        }
      }
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // 指数退避
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
};

// 请求去重（防止重复请求）
const pendingRequests = new Map<string, Promise<any>>();

export const withDeduplication = <T>(
  key: string,
  fn: () => Promise<T>
): Promise<T> => {
  if (pendingRequests.has(key)) {
    return pendingRequests.get(key)!;
  }
  
  const promise = fn().finally(() => {
    pendingRequests.delete(key);
  });
  
  pendingRequests.set(key, promise);
  return promise;
};

// 批量请求处理
export const batchRequests = async <T>(
  requests: (() => Promise<T>)[],
  concurrency = 3
): Promise<T[]> => {
  const results: T[] = [];
  
  for (let i = 0; i < requests.length; i += concurrency) {
    const batch = requests.slice(i, i + concurrency);
    const batchResults = await Promise.allSettled(batch.map(req => req()));
    
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results[i + index] = result.value;
      } else {
        console.error(`Batch request ${i + index} failed:`, result.reason);
        // 可以选择抛出错误或返回默认值
        throw result.reason;
      }
    });
  }
  
  return results;
};

// 分页数据处理
export const processPaginatedResponse = <T>(
  response: PaginatedResponse<T>
): {
  items: T[];
  hasMore: boolean;
  nextPage?: number;
} => {
  return {
    items: response.data,
    hasMore: response.pagination.hasNext,
    nextPage: response.pagination.hasNext ? response.pagination.page + 1 : undefined,
  };
};

// 流式数据处理
export class StreamProcessor {
  private decoder = new TextDecoder();
  
  async *processStream(response: Response): AsyncGenerator<StreamChunk, void, unknown> {
    if (!response.body) {
      throw new Error('Response body is empty');
    }
    
    const reader = response.body.getReader();
    let buffer = '';
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        // 解码数据块
        buffer += this.decoder.decode(value, { stream: true });
        
        // 处理完整的行
        let lineEnd;
        while ((lineEnd = buffer.indexOf('\n')) !== -1) {
          const line = buffer.slice(0, lineEnd).trim();
          buffer = buffer.slice(lineEnd + 1);
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              return;
            }
            
            try {
              const chunk = JSON.parse(data) as StreamChunk;
              yield chunk;
            } catch (error) {
              console.warn('Failed to parse stream chunk:', data);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}

// 请求缓存
const responseCache = new Map<string, { data: any; expires: number }>();

export const withCache = async <T>(
  key: string,
  fn: () => Promise<T>,
  ttl = 300000 // 5分钟
): Promise<T> => {
  const now = Date.now();
  const cached = responseCache.get(key);
  
  if (cached && now < cached.expires) {
    return cached.data;
  }
  
  const data = await fn();
  responseCache.set(key, { data, expires: now + ttl });
  
  return data;
};

// 清理过期缓存
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of responseCache.entries()) {
    if (now >= value.expires) {
      responseCache.delete(key);
    }
  }
}, 60000); // 每分钟清理一次

// 请求元数据提取
export const extractRequestMetadata = (config: any) => {
  return {
    url: config.url,
    method: config.method?.toUpperCase(),
    timestamp: Date.now(),
    requestId: config.headers?.['X-Request-ID'] || generateRequestId(),
  };
};

// 生成请求ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 请求性能监控
export const withPerformanceMonitoring = async <T>(
  fn: () => Promise<T>,
  operationName: string
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    
    console.debug(`API Operation: ${operationName} completed in ${duration}ms`);
    
    // 这里可以发送性能数据到监控服务
    if (duration > 5000) { // 超过5秒的慢请求
      console.warn(`Slow API operation detected: ${operationName} took ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`API Operation: ${operationName} failed after ${duration}ms`, error);
    throw error;
  }
};

// URL安全化
export const sanitizeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    // 移除敏感查询参数
    const sensitiveParams = ['token', 'key', 'secret', 'password'];
    sensitiveParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });
    return urlObj.toString();
  } catch {
    return url;
  }
};