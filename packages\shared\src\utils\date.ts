/**
 * HER 日期时间工具函数
 * 提供统一的日期格式化和处理功能
 */

// 格式化日期为可读格式
export const formatDate = (date: Date | string, locale = 'zh-CN'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '无效日期';
  }
  
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  // 今天
  if (diffDays === 0) {
    return formatTime(dateObj, locale);
  }
  
  // 昨天
  if (diffDays === 1) {
    return `昨天 ${formatTime(dateObj, locale)}`;
  }
  
  // 本周内
  if (diffDays < 7) {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return `周${weekdays[dateObj.getDay()]} ${formatTime(dateObj, locale)}`;
  }
  
  // 本年内
  if (dateObj.getFullYear() === now.getFullYear()) {
    return dateObj.toLocaleDateString(locale, {
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }
  
  // 其他年份
  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 格式化时间
export const formatTime = (date: Date | string, locale = 'zh-CN'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '无效时间';
  }
  
  return dateObj.toLocaleTimeString(locale, {
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 格式化相对时间（多久前）
export const formatRelativeTime = (date: Date | string, locale = 'zh-CN'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return '无效时间';
  }
  
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMinutes < 1) {
    return '刚刚';
  }
  
  if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  }
  
  if (diffHours < 24) {
    return `${diffHours}小时前`;
  }
  
  if (diffDays < 7) {
    return `${diffDays}天前`;
  }
  
  if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7);
    return `${weeks}周前`;
  }
  
  if (diffDays < 365) {
    const months = Math.floor(diffDays / 30);
    return `${months}个月前`;
  }
  
  const years = Math.floor(diffDays / 365);
  return `${years}年前`;
};

// 格式化持续时间
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}天${hours % 24}小时`;
  }
  
  if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`;
  }
  
  if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`;
  }
  
  return `${seconds}秒`;
};

// 获取今天的开始时间
export const getStartOfDay = (date: Date = new Date()): Date => {
  const result = new Date(date);
  result.setHours(0, 0, 0, 0);
  return result;
};

// 获取今天的结束时间
export const getEndOfDay = (date: Date = new Date()): Date => {
  const result = new Date(date);
  result.setHours(23, 59, 59, 999);
  return result;
};

// 获取本周的开始时间（周一）
export const getStartOfWeek = (date: Date = new Date()): Date => {
  const result = new Date(date);
  const day = result.getDay();
  const diff = result.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
  result.setDate(diff);
  result.setHours(0, 0, 0, 0);
  return result;
};

// 获取本月的开始时间
export const getStartOfMonth = (date: Date = new Date()): Date => {
  const result = new Date(date);
  result.setDate(1);
  result.setHours(0, 0, 0, 0);
  return result;
};

// 判断是否是今天
export const isToday = (date: Date | string): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const today = new Date();
  
  return dateObj.getDate() === today.getDate() &&
         dateObj.getMonth() === today.getMonth() &&
         dateObj.getFullYear() === today.getFullYear();
};

// 判断是否是本周
export const isThisWeek = (date: Date | string): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const startOfWeek = getStartOfWeek();
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(endOfWeek.getDate() + 6);
  
  return dateObj >= startOfWeek && dateObj <= endOfWeek;
};

// 获取时间段标识（用于分组显示消息）
export const getTimeGroup = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isToday(dateObj)) {
    return 'today';
  }
  
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  
  if (dateObj.toDateString() === yesterday.toDateString()) {
    return 'yesterday';
  }
  
  if (isThisWeek(dateObj)) {
    return 'this_week';
  }
  
  return dateObj.toISOString().split('T')[0]; // YYYY-MM-DD
};

// 获取友好的日期分组标题
export const getTimeGroupTitle = (group: string): string => {
  switch (group) {
    case 'today':
      return '今天';
    case 'yesterday':
      return '昨天';
    case 'this_week':
      return '本周';
    default:
      // 尝试解析为日期
      const date = new Date(group);
      if (!isNaN(date.getTime())) {
        return formatDate(date);
      }
      return group;
  }
};

// 计算年龄
export const calculateAge = (birthDate: Date | string): number => {
  const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
  const today = new Date();
  
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};

// 时区转换
export const convertToTimezone = (date: Date, timezone: string): Date => {
  try {
    return new Date(date.toLocaleString('en-US', { timeZone: timezone }));
  } catch {
    return date; // 如果时区无效，返回原始日期
  }
};