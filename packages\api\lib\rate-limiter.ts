/**
 * Rate Limiter - 基于Vercel KV的速率限制器
 * 使用方法：withRateLimit(handler, options)包装需要限流的API函数
 */

import { kv } from '@vercel/kv';
import { VercelRequest, VercelResponse } from '@vercel/node';
import { StandardAPIError } from '../middleware/error-handler';

export interface RateLimitOptions {
  maxRequests: number;    // 最大请求次数
  windowMs: number;       // 时间窗口（毫秒）
  keyGenerator?: (req: VercelRequest) => string; // 自定义key生成器
  skipSuccessfulRequests?: boolean; // 是否跳过成功的请求
  skipFailedRequests?: boolean;     // 是否跳过失败的请求
  message?: string;       // 自定义限流消息
}

export interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * Rate Limiter 类
 */
export class RateLimiter {
  private options: Required<Omit<RateLimitOptions, 'keyGenerator'>> & 
    Pick<RateLimitOptions, 'keyGenerator'>;

  constructor(options: RateLimitOptions) {
    this.options = {
      maxRequests: options.maxRequests,
      windowMs: options.windowMs,
      keyGenerator: options.keyGenerator,
      skipSuccessfulRequests: options.skipSuccessfulRequests ?? false,
      skipFailedRequests: options.skipFailedRequests ?? false,
      message: options.message ?? '请求过于频繁，请稍后再试',
    };
  }

  /**
   * 检查是否超过速率限制
   */
  async checkLimit(req: VercelRequest): Promise<RateLimitResult> {
    const key = this.generateKey(req);
    const now = Date.now();
    const windowStart = now - this.options.windowMs;

    try {
      // 使用Redis的滑动窗口算法
      const current = await this.getCurrentCount(key, windowStart, now);
      const remaining = Math.max(0, this.options.maxRequests - current);
      const resetTime = now + this.options.windowMs;

      const result: RateLimitResult = {
        allowed: current < this.options.maxRequests,
        limit: this.options.maxRequests,
        remaining,
        resetTime,
      };

      if (!result.allowed) {
        result.retryAfter = Math.ceil(this.options.windowMs / 1000);
      }

      return result;
    } catch (error) {
      console.error('Rate limiter error:', error);
      // 如果Redis出错，允许请求通过（fail open）
      return {
        allowed: true,
        limit: this.options.maxRequests,
        remaining: this.options.maxRequests,
        resetTime: now + this.options.windowMs,
      };
    }
  }

  /**
   * 增加请求计数
   */
  async incrementCount(req: VercelRequest): Promise<void> {
    const key = this.generateKey(req);
    const now = Date.now();
    const windowStart = now - this.options.windowMs;

    try {
      // 使用有序集合实现滑动窗口
      await this.addRequest(key, now);
      await this.cleanupOldRequests(key, windowStart);
    } catch (error) {
      console.error('Failed to increment rate limit count:', error);
      // 继续执行，不阻塞请求
    }
  }

  /**
   * 生成缓存key
   */
  private generateKey(req: VercelRequest): string {
    if (this.options.keyGenerator) {
      return `ratelimit:${this.options.keyGenerator(req)}`;
    }

    // 默认基于用户ID + IP地址
    const userId = (req as any).userId || 'anonymous';
    const ip = this.getClientIP(req);
    return `ratelimit:${userId}:${ip}`;
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIP(req: VercelRequest): string {
    const forwarded = req.headers['x-forwarded-for'] as string;
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    return req.headers['x-real-ip'] as string || 
           req.connection?.remoteAddress || 
           'unknown';
  }

  /**
   * 获取当前时间窗口内的请求数量
   */
  private async getCurrentCount(key: string, windowStart: number, now: number): Promise<number> {
    // 使用ZCOUNT获取时间窗口内的请求数量
    const count = await kv.zcount(key, windowStart, now);
    return count || 0;
  }

  /**
   * 添加请求记录
   */
  private async addRequest(key: string, timestamp: number): Promise<void> {
    // 使用ZADD添加请求记录，score为时间戳
    await kv.zadd(key, { score: timestamp, member: `${timestamp}-${Math.random()}` });
    // 设置key的过期时间
    await kv.expire(key, Math.ceil(this.options.windowMs / 1000) + 1);
  }

  /**
   * 清理过期的请求记录
   */
  private async cleanupOldRequests(key: string, windowStart: number): Promise<void> {
    // 删除时间窗口外的旧记录
    await kv.zremrangebyscore(key, 0, windowStart);
  }
}

/**
 * 预定义的速率限制配置
 */
export const RATE_LIMIT_CONFIGS = {
  // 聊天API限制
  CHAT: {
    maxRequests: 20,
    windowMs: 60 * 1000, // 1分钟
    message: '聊天请求过于频繁，请稍后再试',
  },

  // 认证API限制
  AUTH: {
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15分钟
    message: '登录尝试过于频繁，请稍后再试',
  },

  // 一般API限制
  GENERAL: {
    maxRequests: 100,
    windowMs: 60 * 1000, // 1分钟
    message: 'API请求过于频繁，请稍后再试',
  },

  // 搜索API限制
  SEARCH: {
    maxRequests: 30,
    windowMs: 60 * 1000, // 1分钟
    message: '搜索请求过于频繁，请稍后再试',
  },
} as const;

/**
 * Rate limit 中间件包装器
 */
export function withRateLimit<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>,
  options: RateLimitOptions
) {
  const rateLimiter = new RateLimiter(options);

  return async (req: VercelRequest, res: VercelResponse, ...args: T): Promise<R | void> => {
    // 预检请求跳过限流
    if (req.method === 'OPTIONS') {
      return handler(req, res, ...args);
    }

    // 检查速率限制
    const result = await rateLimiter.checkLimit(req);

    // 设置响应头
    res.setHeader('X-RateLimit-Limit', result.limit);
    res.setHeader('X-RateLimit-Remaining', result.remaining);
    res.setHeader('X-RateLimit-Reset', new Date(result.resetTime).toISOString());

    if (!result.allowed) {
      if (result.retryAfter) {
        res.setHeader('Retry-After', result.retryAfter);
      }

      const error = new StandardAPIError(
        options.message || '请求过于频繁，请稍后再试',
        'RATE_LIMITED',
        429,
        {
          limit: result.limit,
          remaining: result.remaining,
          resetTime: result.resetTime,
          retryAfter: result.retryAfter,
        }
      );

      throw error;
    }

    // 增加计数（在请求开始时）
    await rateLimiter.incrementCount(req);

    // 继续处理请求
    return handler(req, res, ...args);
  };
}

/**
 * 聊天API专用速率限制中间件
 */
export function withChatRateLimit<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>
) {
  return withRateLimit(handler, RATE_LIMIT_CONFIGS.CHAT);
}

/**
 * 认证API专用速率限制中间件
 */
export function withAuthRateLimit<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>
) {
  return withRateLimit(handler, RATE_LIMIT_CONFIGS.AUTH);
}

/**
 * 基于用户ID的速率限制
 */
export function withUserRateLimit<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>,
  options: Omit<RateLimitOptions, 'keyGenerator'>
) {
  return withRateLimit(handler, {
    ...options,
    keyGenerator: (req) => {
      const userId = (req as any).userId || 'anonymous';
      return `user:${userId}`;
    },
  });
}

/**
 * 基于IP的速率限制
 */
export function withIPRateLimit<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>,
  options: Omit<RateLimitOptions, 'keyGenerator'>
) {
  const rateLimiter = new RateLimiter({
    ...options,
    keyGenerator: (req) => {
      const forwarded = req.headers['x-forwarded-for'] as string;
      const ip = forwarded ? forwarded.split(',')[0].trim() : 
                (req.headers['x-real-ip'] as string || 
                 req.connection?.remoteAddress || 
                 'unknown');
      return `ip:${ip}`;
    },
  });

  return withRateLimit(handler, {
    ...options,
    keyGenerator: rateLimiter.options.keyGenerator,
  });
}

/**
 * 获取速率限制状态（不增加计数）
 */
export async function getRateLimitStatus(
  req: VercelRequest,
  options: RateLimitOptions
): Promise<RateLimitResult> {
  const rateLimiter = new RateLimiter(options);
  return rateLimiter.checkLimit(req);
}

/**
 * 手动重置速率限制计数
 */
export async function resetRateLimit(req: VercelRequest, options: RateLimitOptions): Promise<void> {
  const rateLimiter = new RateLimiter(options);
  const key = (rateLimiter as any).generateKey(req);
  
  try {
    await kv.del(key);
  } catch (error) {
    console.error('Failed to reset rate limit:', error);
  }
}