/**
 * HER Mobile App Entry Point
 * 私密AI陪伴应用主入口
 */

import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';

import { ThemeProvider } from '@her/ui';
import { RootNavigator } from './src/navigation/RootNavigator';
import { useAppStore } from './src/stores/appStore';
import { ErrorBoundary } from './src/components/ErrorBoundary';

// 防止启动画面自动隐藏
SplashScreen.preventAutoHideAsync();

export default function App() {
  const [appIsReady, setAppIsReady] = useState(false);
  const { theme, initializeApp } = useAppStore();

  useEffect(() => {
    async function prepare() {
      try {
        // 预加载字体
        await Font.loadAsync({
          'Inter': require('./assets/fonts/Inter.ttf'),
          'Nunito': require('./assets/fonts/Nunito.ttf'),
          'PingFangSC': require('./assets/fonts/PingFang.ttf'),
        });

        // 初始化应用状态
        await initializeApp();

        // 模拟最小启动时间以确保良好的用户体验
        await new Promise(resolve => setTimeout(resolve, 1500));
      } catch (e) {
        console.warn('App preparation error:', e);
      } finally {
        setAppIsReady(true);
      }
    }

    prepare();
  }, [initializeApp]);

  useEffect(() => {
    if (appIsReady) {
      SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ThemeProvider variant={theme.variant} mode={theme.mode}>
            <RootNavigator />
            <StatusBar style={theme.isDark ? 'light' : 'dark'} />
          </ThemeProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}