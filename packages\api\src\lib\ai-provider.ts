/**
 * AI Provider 配置
 * 支持 OpenAI 直连和 OpenRouter
 * 使用方法：配置OPENROUTER_API_KEY环境变量，即可通过OpenRouter访问GPT-4等模型
 */

import OpenAI from 'openai';
import { getAIConfig, getAppConfig } from '../../lib/config';

export type AIProvider = 'openai' | 'openrouter';

export interface AIConfig {
  provider: AIProvider;
  apiKey: string;
  baseURL?: string;
  model: string;
  embeddingModel?: string;
}

/**
 * OpenRouter客户端类
 * 使用新版OpenAI SDK，完全兼容OpenRouter API
 */
export class OpenRouterClient {
  private client: OpenAI;
  private model: string;
  private embeddingModel: string;

  constructor(config: AIConfig) {
    const isOpenRouter = config.provider === 'openrouter';
    
    const appConfig = getAppConfig();
    
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: isOpenRouter 
        ? 'https://openrouter.ai/api/v1'
        : config.baseURL || 'https://api.openai.com/v1',
      defaultHeaders: isOpenRouter ? {
        'HTTP-Referer': appConfig.url,
        'X-Title': appConfig.name,
      } : undefined,
    });

    this.model = config.model;
    this.embeddingModel = config.embeddingModel || 'openai/text-embedding-3-small';
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [{ role: 'user', content: 'ping' }],
        max_tokens: 5,
      });
      return !!response.choices[0]?.message?.content;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  }

  /**
   * 发送聊天消息
   */
  async chat(messages: OpenAI.Chat.ChatCompletionMessageParam[], options: {
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  } = {}) {
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages,
        temperature: options.temperature || 0.8, // 稍高温度，更人性化
        max_tokens: options.maxTokens || 1000,
        stream: options.stream || false,
        ...options,
      });

      return response;
    } catch (error: any) {
      console.error('AI Chat Error:', error.response?.data || error.message);
      throw new Error(`AI 服务错误: ${error.message}`);
    }
  }

  /**
   * 生成文本嵌入向量
   */
  async createEmbedding(text: string): Promise<number[] | null> {
    try {
      const response = await this.client.embeddings.create({
        model: this.embeddingModel,
        input: text,
      });

      return response.data[0].embedding;
    } catch (error: any) {
      console.error('Embedding Error:', error.response?.data || error.message);
      return null;
    }
  }

  /**
   * 流式对话 (返回AsyncGenerator)
   */
  async *chatStream(
    messages: OpenAI.Chat.ChatCompletionMessageParam[], 
    options: {
      temperature?: number;
      maxTokens?: number;
    } = {}
  ): AsyncGenerator<string, void, unknown> {
    try {
      const stream = await this.client.chat.completions.create({
        model: this.model,
        messages,
        temperature: options.temperature || 0.8,
        max_tokens: options.maxTokens || 1000,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content;
        if (content) {
          yield content;
        }
      }
    } catch (error: any) {
      console.error('Stream Error:', error);
      throw new Error(`流式对话错误: ${error.message}`);
    }
  }
}

/**
 * 从配置管理器创建 AI 客户端
 */
export function createAIClientFromEnv(): OpenRouterClient {
  const aiConfig = getAIConfig();
  
  if (aiConfig.provider === 'openrouter') {
    return new OpenRouterClient({
      provider: 'openrouter',
      apiKey: aiConfig.openrouter.apiKey,
      model: aiConfig.openrouter.model,
      embeddingModel: aiConfig.openrouter.embeddingModel,
    });
  } else {
    if (!aiConfig.openai.apiKey) {
      throw new Error('OpenAI API密钥未配置，请设置OPENAI_API_KEY环境变量');
    }
    
    return new OpenRouterClient({
      provider: 'openai',
      apiKey: aiConfig.openai.apiKey,
      model: aiConfig.openai.model || 'gpt-4-turbo',
      embeddingModel: aiConfig.openrouter.embeddingModel, // 统一使用embedding配置
    });
  }
}

/**
 * 创建默认的OpenRouter客户端实例
 */
export const aiClient = createAIClientFromEnv();

/**
 * OpenRouter 支持的模型列表（2025年更新）
 */
export const OPENROUTER_MODELS = {
  // OpenAI 模型 - 推荐用于HER项目
  'gpt-4-turbo': 'openai/gpt-4-turbo',           // 平衡性能和成本
  'gpt-4o': 'openai/gpt-4o',                     // 最新多模态模型
  'gpt-4': 'openai/gpt-4',                       // 经典GPT-4
  'gpt-3.5-turbo': 'openai/gpt-3.5-turbo',      // 性价比之选
  
  // Anthropic 模型 - 情感理解出色
  'claude-3-opus': 'anthropic/claude-3-opus:beta',     // 最强推理
  'claude-3-sonnet': 'anthropic/claude-3-sonnet:beta', // 平衡选择
  'claude-3-haiku': 'anthropic/claude-3-haiku:beta',   // 快速响应
  
  // Google 模型
  'gemini-pro': 'google/gemini-pro',
  'gemini-pro-1.5': 'google/gemini-pro-1.5',
  
  // 开源模型 - 成本友好
  'llama-3.1-70b': 'meta-llama/llama-3.1-70b-instruct',
  'llama-3.1-8b': 'meta-llama/llama-3.1-8b-instruct',
  'mixtral-8x7b': 'mistralai/mixtral-8x7b-instruct',
} as const;

/**
 * 推荐配置：根据使用场景选择模型
 */
export const RECOMMENDED_MODELS = {
  // HER应用推荐配置
  primary: 'openai/gpt-4-turbo',        // 主要对话模型
  fallback: 'openai/gpt-3.5-turbo',     // 降级模型  
  emotional: 'anthropic/claude-3-sonnet:beta', // 情感支持
  creative: 'openai/gpt-4o',            // 创意内容
  economical: 'meta-llama/llama-3.1-8b-instruct', // 经济选择
} as const;

/**
 * 模型价格参考 (每1M tokens，美元，2025年)
 */
export const MODEL_PRICING = {
  'openai/gpt-4-turbo': { input: 10, output: 30 },
  'openai/gpt-4o': { input: 5, output: 15 },
  'openai/gpt-3.5-turbo': { input: 0.5, output: 1.5 },
  'anthropic/claude-3-sonnet:beta': { input: 3, output: 15 },
  'anthropic/claude-3-haiku:beta': { input: 0.25, output: 1.25 },
  'meta-llama/llama-3.1-8b-instruct': { input: 0.18, output: 0.18 },
} as const;