/**
 * 数据库连接池管理器
 * 优化Supabase客户端连接，避免重复创建连接
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { getDatabaseConfig, isDevelopment } from './config';

/**
 * 连接池配置
 */
interface PoolConfig {
  maxConnections: number;
  idleTimeout: number;
  connectionTimeout: number;
  retryDelay: number;
  maxRetries: number;
}

/**
 * 连接池管理器
 */
export class DatabaseConnectionPool {
  private static instance: DatabaseConnectionPool;
  private clients: Map<string, SupabaseClient> = new Map();
  private config: PoolConfig;
  private dbConfig: ReturnType<typeof getDatabaseConfig>;

  private constructor() {
    this.dbConfig = getDatabaseConfig();
    this.config = {
      maxConnections: 10,
      idleTimeout: 30000, // 30秒
      connectionTimeout: 5000, // 5秒
      retryDelay: 1000, // 1秒
      maxRetries: 3,
    };

    // 清理定时器
    this.setupCleanupTimer();
  }

  /**
   * 获取连接池实例
   */
  static getInstance(): DatabaseConnectionPool {
    if (!DatabaseConnectionPool.instance) {
      DatabaseConnectionPool.instance = new DatabaseConnectionPool();
    }
    return DatabaseConnectionPool.instance;
  }

  /**
   * 获取数据库客户端 (管理员权限)
   */
  getAdminClient(): SupabaseClient {
    const key = 'admin';
    
    if (!this.clients.has(key)) {
      const client = createClient(
        this.dbConfig.url,
        this.dbConfig.serviceKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          db: {
            schema: 'public',
          },
          realtime: {
            params: {
              eventsPerSecond: 10,
            },
          },
        }
      );

      this.clients.set(key, client);
      
      if (isDevelopment()) {
        console.log(`🗄️  Created new Supabase admin client`);
      }
    }

    return this.clients.get(key)!;
  }

  /**
   * 获取用户客户端 (RLS权限)
   */
  getUserClient(userToken: string): SupabaseClient {
    const key = `user:${this.hashToken(userToken)}`;
    
    if (!this.clients.has(key)) {
      const client = createClient(
        this.dbConfig.url,
        this.dbConfig.serviceKey, // 使用service key但设置用户session
        {
          global: {
            headers: {
              Authorization: `Bearer ${userToken}`,
            },
          },
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          db: {
            schema: 'public',
          },
        }
      );

      this.clients.set(key, client);
      
      if (isDevelopment()) {
        console.log(`🗄️  Created new Supabase user client for ${key}`);
      }
    }

    return this.clients.get(key)!;
  }

  /**
   * 批量操作客户端
   */
  getBatchClient(): SupabaseClient {
    const key = 'batch';
    
    if (!this.clients.has(key)) {
      const client = createClient(
        this.dbConfig.url,
        this.dbConfig.serviceKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          db: {
            schema: 'public',
          },
          // 批量操作优化
          global: {
            headers: {
              'Prefer': 'return=minimal', // 减少返回数据
            },
          },
        }
      );

      this.clients.set(key, client);
    }

    return this.clients.get(key)!;
  }

  /**
   * 实时订阅客户端
   */
  getRealtimeClient(): SupabaseClient {
    const key = 'realtime';
    
    if (!this.clients.has(key)) {
      const client = createClient(
        this.dbConfig.url,
        this.dbConfig.serviceKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          realtime: {
            params: {
              eventsPerSecond: 20,
            },
          },
        }
      );

      this.clients.set(key, client);
    }

    return this.clients.get(key)!;
  }

  /**
   * 清理连接
   */
  cleanup(clientKey?: string): void {
    if (clientKey) {
      const client = this.clients.get(clientKey);
      if (client) {
        // Supabase客户端没有显式关闭方法，但可以取消所有订阅
        this.clients.delete(clientKey);
        
        if (isDevelopment()) {
          console.log(`🗄️  Cleaned up client: ${clientKey}`);
        }
      }
    } else {
      // 清理所有连接
      this.clients.clear();
      
      if (isDevelopment()) {
        console.log(`🗄️  Cleaned up all database connections`);
      }
    }
  }

  /**
   * 获取连接池状态
   */
  getStats() {
    return {
      activeConnections: this.clients.size,
      maxConnections: this.config.maxConnections,
      connections: Array.from(this.clients.keys()),
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const client = this.getAdminClient();
      const { error } = await client.from('users').select('id').limit(1);
      return !error;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Token哈希 (用于缓存key)
   */
  private hashToken(token: string): string {
    // 简单哈希，实际应用中可以使用更复杂的哈希算法
    let hash = 0;
    for (let i = 0; i < token.length; i++) {
      const char = token.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每5分钟清理一次非活跃连接
    setInterval(() => {
      if (this.clients.size > this.config.maxConnections) {
        // 如果连接数超过最大值，清理一些连接
        // 这里可以实现更智能的清理策略
        if (isDevelopment()) {
          console.log('🗄️  Connection pool cleanup triggered');
        }
      }
    }, 5 * 60 * 1000);
  }
}

/**
 * 数据库操作助手类
 */
export class DatabaseHelper {
  private pool: DatabaseConnectionPool;

  constructor() {
    this.pool = DatabaseConnectionPool.getInstance();
  }

  /**
   * 执行管理员查询
   */
  async adminQuery<T = any>(
    operation: (client: SupabaseClient) => Promise<T>
  ): Promise<T> {
    const client = this.pool.getAdminClient();
    return operation(client);
  }

  /**
   * 执行用户查询 (RLS)
   */
  async userQuery<T = any>(
    userToken: string,
    operation: (client: SupabaseClient) => Promise<T>
  ): Promise<T> {
    const client = this.pool.getUserClient(userToken);
    return operation(client);
  }

  /**
   * 批量操作
   */
  async batchOperation<T = any>(
    operation: (client: SupabaseClient) => Promise<T>
  ): Promise<T> {
    const client = this.pool.getBatchClient();
    return operation(client);
  }

  /**
   * 事务操作 (Supabase不直接支持事务，但可以通过RPC实现)
   */
  async transaction<T = any>(
    operations: Array<(client: SupabaseClient) => Promise<any>>
  ): Promise<T[]> {
    const client = this.pool.getAdminClient();
    const results: T[] = [];

    // 顺序执行所有操作
    for (const operation of operations) {
      const result = await operation(client);
      results.push(result);
    }

    return results;
  }

  /**
   * 重试机制包装
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }

        // 数据库连接错误才重试
        if (this.shouldRetry(error)) {
          await new Promise(resolve => setTimeout(resolve, delayMs * (attempt + 1)));
        } else {
          break;
        }
      }
    }

    throw lastError;
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any): boolean {
    // 网络错误或超时错误
    if (error.code === 'PGRST301' || error.message?.includes('timeout')) {
      return true;
    }
    
    // 连接错误
    if (error.message?.includes('connection') || error.message?.includes('network')) {
      return true;
    }

    return false;
  }
}

// 导出单例实例
export const dbPool = DatabaseConnectionPool.getInstance();
export const dbHelper = new DatabaseHelper();

/**
 * 便捷的数据库操作函数
 */

/**
 * 获取管理员客户端
 */
export function getAdminClient(): SupabaseClient {
  return dbPool.getAdminClient();
}

/**
 * 获取用户客户端
 */
export function getUserClient(userToken: string): SupabaseClient {
  return dbPool.getUserClient(userToken);
}

/**
 * 获取批量操作客户端
 */
export function getBatchClient(): SupabaseClient {
  return dbPool.getBatchClient();
}

/**
 * 执行管理员查询
 */
export async function adminQuery<T = any>(
  operation: (client: SupabaseClient) => Promise<T>
): Promise<T> {
  return dbHelper.adminQuery(operation);
}

/**
 * 执行用户查询
 */
export async function userQuery<T = any>(
  userToken: string,
  operation: (client: SupabaseClient) => Promise<T>
): Promise<T> {
  return dbHelper.userQuery(userToken, operation);
}

/**
 * 数据库健康检查
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  return dbPool.healthCheck();
}

/**
 * 获取连接池统计
 */
export function getDatabaseStats() {
  return dbPool.getStats();
}