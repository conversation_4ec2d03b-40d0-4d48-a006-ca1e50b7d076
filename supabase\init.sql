-- HER 数据库初始化脚本
-- 在 Supabase SQL Editor 中运行此脚本

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  phone TEXT UNIQUE,
  nickname TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  preferences JSONB DEFAULT '{
    "theme": "auto",
    "notifications": true,
    "voice_enabled": false
  }'::jsonb,
  is_anonymous BOOLEAN DEFAULT true
);

-- 创建对话表
CREATE TABLE IF NOT EXISTS conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  summary TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb,
  is_active BOOLEAN DEFAULT true
);

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  sender TEXT NOT NULL CHECK (sender IN ('user', 'her')),
  content TEXT NOT NULL,
  emotion TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 创建记忆表 (支持向量搜索)
CREATE TABLE IF NOT EXISTS memories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536), -- OpenAI embedding 维度
  importance FLOAT DEFAULT 0.5 CHECK (importance >= 0 AND importance <= 1),
  category TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  access_count INTEGER DEFAULT 0,
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 创建情绪记录表
CREATE TABLE IF NOT EXISTS mood_checkins (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  mood_value FLOAT NOT NULL CHECK (mood_value >= 0 AND mood_value <= 1),
  mood_label TEXT,
  note TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 创建索引以提升性能
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_conversations_user ON conversations(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_memories_user ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_importance ON memories(importance DESC);
CREATE INDEX IF NOT EXISTS idx_mood_checkins_user_date ON mood_checkins(user_id, created_at DESC);

-- 为向量搜索创建索引
CREATE INDEX IF NOT EXISTS idx_memories_embedding ON memories 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- 启用行级安全 (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_checkins ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" 
  ON users FOR SELECT 
  USING (auth.uid()::text = id::text);

CREATE POLICY "Users can update own profile" 
  ON users FOR UPDATE 
  USING (auth.uid()::text = id::text);

CREATE POLICY "Users can insert own profile" 
  ON users FOR INSERT 
  WITH CHECK (auth.uid()::text = id::text);

-- 对话访问策略
CREATE POLICY "Users can manage own conversations" 
  ON conversations FOR ALL 
  USING (auth.uid()::text = user_id::text);

-- 消息访问策略
CREATE POLICY "Users can manage messages in own conversations" 
  ON messages FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id::text = auth.uid()::text
    )
  );

-- 记忆访问策略
CREATE POLICY "Users can manage own memories" 
  ON memories FOR ALL 
  USING (auth.uid()::text = user_id::text);

-- 情绪记录访问策略
CREATE POLICY "Users can manage own mood checkins" 
  ON mood_checkins FOR ALL 
  USING (auth.uid()::text = user_id::text);

-- 创建实用函数

-- 获取用户最近的对话
CREATE OR REPLACE FUNCTION get_recent_conversations(p_user_id UUID, p_limit INT DEFAULT 10)
RETURNS TABLE (
  id UUID,
  title TEXT,
  updated_at TIMESTAMP WITH TIME ZONE,
  last_message TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.title,
    c.updated_at,
    (SELECT m.content FROM messages m 
     WHERE m.conversation_id = c.id 
     ORDER BY m.created_at DESC LIMIT 1) as last_message
  FROM conversations c
  WHERE c.user_id = p_user_id
  ORDER BY c.updated_at DESC
  LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- 搜索相似记忆 (向量相似度搜索)
CREATE OR REPLACE FUNCTION search_memories(
  query_embedding vector(1536),
  match_count INT DEFAULT 5,
  p_user_id UUID DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  content TEXT,
  similarity FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.content,
    1 - (m.embedding <=> query_embedding) AS similarity
  FROM memories m
  WHERE (p_user_id IS NULL OR m.user_id = p_user_id)
  ORDER BY m.embedding <=> query_embedding
  LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- 统计用户情绪趋势
CREATE OR REPLACE FUNCTION get_mood_trend(
  p_user_id UUID,
  p_days INT DEFAULT 7
)
RETURNS TABLE (
  date DATE,
  avg_mood FLOAT,
  checkin_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    DATE(created_at) as date,
    AVG(mood_value) as avg_mood,
    COUNT(*) as checkin_count
  FROM mood_checkins
  WHERE user_id = p_user_id
    AND created_at > NOW() - INTERVAL '1 day' * p_days
  GROUP BY DATE(created_at)
  ORDER BY date DESC;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器更新 updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_conversations_updated_at
BEFORE UPDATE ON conversations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建测试数据（仅开发环境）
-- 注意：生产环境请注释掉这部分

-- 创建测试用户
INSERT INTO users (id, phone, nickname, is_anonymous)
VALUES 
  ('11111111-1111-1111-1111-111111111111', '13800138000', '测试用户', false)
ON CONFLICT (phone) DO NOTHING;

-- 创建测试对话
INSERT INTO conversations (id, user_id, title)
VALUES 
  ('22222222-2222-2222-2222-222222222222', 
   '11111111-1111-1111-1111-111111111111', 
   '第一次对话')
ON CONFLICT (id) DO NOTHING;

-- 创建测试消息
INSERT INTO messages (conversation_id, sender, content, emotion)
VALUES 
  ('22222222-2222-2222-2222-222222222222', 'her', '你好呀，很高兴认识你！有什么想聊的吗？', 'warm'),
  ('22222222-2222-2222-2222-222222222222', 'user', '最近有点累，想找个人说说话', 'tired'),
  ('22222222-2222-2222-2222-222222222222', 'her', '我在这里陪着你呢。累了就是要好好休息，想说什么都可以告诉我。', 'caring')
ON CONFLICT (id) DO NOTHING;

-- 输出成功信息
SELECT 'HER 数据库初始化成功！' as message;