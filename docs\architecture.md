# HER Fullstack Architecture Document
> 私密AI陪伴应用 - 全栈架构设计 v1.0

## Introduction

本文档描述HER应用的完整全栈架构，包括前端实现、后端系统及其集成。这是一个为现代都市人提供私密AI陪伴服务的移动应用，采用 **快速上线、全球可达、小而美** 的设计理念。

本架构设计基于以下核心原则：
- **隐私优先**：端到端加密，本地存储优先
- **情感响应**：低延迟，流式交互
- **全球可达**：CDN加速，边缘计算
- **成本优化**：Serverless架构，按需扩展

### Starter Template or Existing Project
N/A - Greenfield project（全新项目）

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-09 | 1.0 | Initial architecture design | Winston |

---

## High Level Architecture

### Technical Summary
HER采用现代化的Serverless架构，通过React Native构建跨平台移动应用，使用Vercel Edge Functions提供低延迟的API服务，Supabase作为一站式后端解决方案提供数据库、认证和实时功能，通过Cloudflare CDN实现全球加速。整个架构围绕"快速响应"和"隐私保护"两个核心目标设计，通过边缘计算和本地缓存策略，确保用户获得温暖、即时的陪伴体验。

### Platform and Infrastructure Choice
**Platform:** Vercel + Supabase + Cloudflare
**Key Services:** 
- Vercel: Edge Functions, Static Hosting, KV Storage
- Supabase: PostgreSQL, pgvector, Auth, Realtime, Storage
- Cloudflare: CDN, DNS, WAF, DDoS Protection
- OpenAI: GPT-4 Turbo, Embeddings API

**Deployment Regions:** 
- Vercel: Global Edge Network (自动选择最近节点)
- Supabase: Singapore (ap-southeast-1) - 亚洲用户优化
- Cloudflare: Global (280+ cities)

### Repository Structure
**Structure:** Monorepo
**Monorepo Tool:** pnpm workspaces
**Package Organization:**
```
her/
├── apps/
│   ├── mobile/        # React Native应用
│   └── web/          # 管理后台(可选)
├── packages/
│   ├── api/          # Vercel Functions
│   ├── shared/       # 共享类型和工具
│   └── ui/           # 共享UI组件
└── services/         # 后端服务配置
```

### High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        iOS[iOS App]
        Android[Android App]
    end
    
    subgraph "Edge Layer - Cloudflare"
        CDN[CDN/Cache]
        WAF[WAF/DDoS]
    end
    
    subgraph "Application Layer - Vercel"
        EF[Edge Functions]
        KV[KV Cache]
        Static[Static Assets]
    end
    
    subgraph "Backend Layer - Supabase"
        Auth[Auth Service]
        DB[(PostgreSQL)]
        Vector[(pgvector)]
        RT[Realtime]
        Storage[Storage]
    end
    
    subgraph "AI Layer"
        GPT[OpenAI GPT-4]
        EMB[Embeddings]
    end
    
    iOS --> CDN
    Android --> CDN
    CDN --> WAF
    WAF --> EF
    EF <--> KV
    EF <--> Auth
    EF <--> DB
    EF <--> Vector
    EF <--> RT
    EF <--> Storage
    EF --> GPT
    EF --> EMB
    
    Static --> CDN
```

### Architectural Patterns
- **Jamstack Architecture:** 静态资源 + Serverless API - _Rationale:_ 最佳性能和可扩展性，降低运维复杂度
- **Edge Computing:** 边缘函数处理API请求 - _Rationale:_ 降低全球用户访问延迟
- **Event-Driven:** 使用Server-Sent Events实现流式响应 - _Rationale:_ 实现打字机效果，提升对话体验
- **Offline-First:** 本地缓存优先策略 - _Rationale:_ 提升用户体验，支持离线使用
- **Repository Pattern:** 抽象数据访问层 - _Rationale:_ 便于测试和未来数据库迁移
- **BFF (Backend for Frontend):** 专门的移动端API - _Rationale:_ 优化移动端数据传输
- **Circuit Breaker:** API熔断机制 - _Rationale:_ 防止级联故障，提高系统稳定性

---

## Tech Stack

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | 类型安全的JavaScript | 提高代码质量，减少运行时错误 |
| Frontend Framework | React Native | 0.73+ | 跨平台移动应用开发 | 一套代码支持iOS/Android |
| UI Component Library | React Native Elements | 4.0+ | UI组件库 | 快速构建一致的界面 |
| State Management | Zustand | 4.5+ | 轻量级状态管理 | 简单易用，性能优秀 |
| Backend Language | TypeScript/Node.js | 20 LTS | 后端开发语言 | 前后端语言统一 |
| Backend Framework | Vercel Functions | Latest | Serverless函数 | 自动扩展，按需计费 |
| API Style | REST + SSE | - | API通信协议 | 简单可靠，支持流式响应 |
| Database | PostgreSQL | 15 | 主数据库 | 成熟稳定，功能丰富 |
| Vector Database | pgvector | 0.5+ | 向量存储与搜索 | 原生集成，无需额外服务 |
| Cache | Vercel KV | Latest | Redis缓存 | 低延迟，全球分布 |
| File Storage | Supabase Storage | Latest | 文件存储 | 集成认证，简单易用 |
| Authentication | Supabase Auth | Latest | 用户认证 | 支持匿名登录，集成完善 |
| Frontend Testing | Jest + React Native Testing Library | Latest | 单元/集成测试 | 官方推荐，生态成熟 |
| Backend Testing | Vitest | Latest | API测试 | 快速，兼容Jest |
| E2E Testing | Detox | 20+ | 端到端测试 | React Native专用 |
| Build Tool | Metro | Latest | RN打包工具 | 官方默认打包器 |
| Bundler | Webpack | 5+ | Web资源打包 | 管理后台使用 |
| IaC Tool | Terraform | 1.6+ | 基础设施即代码 | 管理云资源 |
| CI/CD | GitHub Actions | - | 持续集成/部署 | 集成度高，配置简单 |
| Monitoring | Vercel Analytics + Sentry | Latest | 监控和错误追踪 | 实时监控，快速定位问题 |
| Logging | Vercel Logs | Latest | 日志收集 | 自动收集，易于查询 |
| CSS Framework | NativeWind | 3.0+ | 样式框架 | Tailwind for React Native |

---

## Data Models

### User Model
**Purpose:** 存储用户基本信息和偏好设置

**Key Attributes:**
- `id`: uuid - 用户唯一标识
- `created_at`: timestamp - 注册时间
- `phone`: string (nullable) - 手机号(可选)
- `nickname`: string (nullable) - 昵称
- `avatar_url`: string (nullable) - 头像URL
- `preferences`: jsonb - 用户偏好设置
- `is_anonymous`: boolean - 是否匿名用户
- `last_seen_at`: timestamp - 最后活跃时间

**TypeScript Interface:**
```typescript
interface User {
  id: string;
  createdAt: Date;
  phone?: string;
  nickname?: string;
  avatarUrl?: string;
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    notificationEnabled: boolean;
    voiceEnabled: boolean;
    language: string;
  };
  isAnonymous: boolean;
  lastSeenAt: Date;
}
```

**Relationships:**
- Has many Conversations
- Has many MoodCheckIns

### Conversation Model
**Purpose:** 管理用户的对话会话

**Key Attributes:**
- `id`: uuid - 对话唯一标识
- `user_id`: uuid - 所属用户
- `title`: string (nullable) - 对话标题(自动生成)
- `created_at`: timestamp - 创建时间
- `updated_at`: timestamp - 更新时间
- `metadata`: jsonb - 对话元数据
- `is_archived`: boolean - 是否归档

**TypeScript Interface:**
```typescript
interface Conversation {
  id: string;
  userId: string;
  title?: string;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    messageCount: number;
    lastMessageAt: Date;
    emotion?: string;
  };
  isArchived: boolean;
}
```

**Relationships:**
- Belongs to User
- Has many Messages

### Message Model
**Purpose:** 存储对话消息内容

**Key Attributes:**
- `id`: uuid - 消息唯一标识
- `conversation_id`: uuid - 所属对话
- `role`: enum - 发送者角色(user/assistant)
- `content`: text - 消息内容(加密存储)
- `created_at`: timestamp - 发送时间
- `metadata`: jsonb - 消息元数据

**TypeScript Interface:**
```typescript
interface Message {
  id: string;
  conversationId: string;
  role: 'user' | 'assistant';
  content: string; // 加密后的内容
  createdAt: Date;
  metadata: {
    emotion?: string;
    tokens?: number;
    model?: string;
  };
}
```

**Relationships:**
- Belongs to Conversation

### Memory Model
**Purpose:** 存储用户记忆和上下文信息

**Key Attributes:**
- `id`: uuid - 记忆唯一标识
- `user_id`: uuid - 所属用户
- `type`: enum - 记忆类型
- `content`: text - 记忆内容
- `embedding`: vector(1536) - 向量表示
- `importance`: float - 重要度分数
- `created_at`: timestamp - 创建时间
- `accessed_at`: timestamp - 最后访问时间

**TypeScript Interface:**
```typescript
interface Memory {
  id: string;
  userId: string;
  type: 'fact' | 'preference' | 'event' | 'emotion';
  content: string;
  embedding: number[]; // 1536维向量
  importance: number; // 0-1
  createdAt: Date;
  accessedAt: Date;
  metadata: {
    source?: string;
    tags?: string[];
    relatedMemories?: string[];
  };
}
```

**Relationships:**
- Belongs to User
- Can relate to other Memories

### MoodCheckIn Model
**Purpose:** 记录用户情绪状态

**Key Attributes:**
- `id`: uuid - 记录唯一标识
- `user_id`: uuid - 所属用户
- `mood_value`: integer - 情绪值(0-100)
- `mood_label`: string - 情绪标签
- `created_at`: timestamp - 记录时间
- `note`: text (nullable) - 备注

**TypeScript Interface:**
```typescript
interface MoodCheckIn {
  id: string;
  userId: string;
  moodValue: number; // 0-100
  moodLabel: string;
  createdAt: Date;
  note?: string;
}
```

**Relationships:**
- Belongs to User

---

## API Specification

### REST API Specification

```yaml
openapi: 3.0.0
info:
  title: HER API
  version: 1.0.0
  description: 私密AI陪伴应用API

servers:
  - url: https://her-api.vercel.app/api
    description: Production API
  - url: http://localhost:3000/api
    description: Local Development

paths:
  # 认证相关
  /auth/anonymous:
    post:
      summary: 匿名登录
      responses:
        200:
          description: 成功返回token
          content:
            application/json:
              schema:
                type: object
                properties:
                  token: 
                    type: string
                  user:
                    $ref: '#/components/schemas/User'
  
  /auth/phone:
    post:
      summary: 手机号登录
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                phone:
                  type: string
                code:
                  type: string
  
  # 对话相关
  /chat/send:
    post:
      summary: 发送消息
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                conversationId:
                  type: string
                message:
                  type: string
                  description: 加密后的消息
      responses:
        200:
          description: 流式返回AI响应
          content:
            text/event-stream:
              schema:
                type: string
  
  /chat/conversations:
    get:
      summary: 获取对话列表
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
  
  /chat/messages/{conversationId}:
    get:
      summary: 获取历史消息
      security:
        - bearerAuth: []
      parameters:
        - name: conversationId
          in: path
          required: true
          schema:
            type: string
  
  # 记忆相关
  /memory/search:
    post:
      summary: 搜索相关记忆
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                query:
                  type: string
                limit:
                  type: integer
                  default: 5
  
  /memory/save:
    post:
      summary: 保存记忆
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Memory'
  
  # 情绪相关
  /mood/checkin:
    post:
      summary: 情绪签到
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                moodValue:
                  type: integer
                  minimum: 0
                  maximum: 100
                moodLabel:
                  type: string
                note:
                  type: string
  
  /mood/history:
    get:
      summary: 获取情绪历史
      security:
        - bearerAuth: []
      parameters:
        - name: days
          in: query
          schema:
            type: integer
            default: 30

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        nickname:
          type: string
        isAnonymous:
          type: boolean
    
    Memory:
      type: object
      properties:
        type:
          type: string
          enum: [fact, preference, event, emotion]
        content:
          type: string
        importance:
          type: number
```

---

## Components

### Mobile App Component
**Responsibility:** React Native移动应用，提供用户界面和交互体验

**Key Interfaces:**
- API Service Layer (HTTP/WebSocket)
- Local Storage (MMKV)
- Biometric Authentication
- Push Notifications

**Dependencies:** Vercel API, Supabase Auth

**Technology Stack:** React Native 0.73, TypeScript, Zustand, MMKV

### API Gateway Component
**Responsibility:** 处理所有API请求，提供统一入口

**Key Interfaces:**
- REST endpoints
- Server-Sent Events for streaming
- WebSocket for realtime

**Dependencies:** Supabase, OpenAI, Vercel KV

**Technology Stack:** Vercel Edge Functions, TypeScript

### Chat Service Component
**Responsibility:** 处理对话逻辑，调用AI模型

**Key Interfaces:**
- `/api/chat/send` - 发送消息
- `/api/chat/stream` - 流式响应
- Memory retrieval interface

**Dependencies:** OpenAI API, Memory Service, Cache Service

**Technology Stack:** TypeScript, OpenAI SDK, Server-Sent Events

### Memory Service Component
**Responsibility:** 管理用户记忆的存储和检索

**Key Interfaces:**
- Vector search API
- Memory CRUD operations
- Embedding generation

**Dependencies:** Supabase pgvector, OpenAI Embeddings

**Technology Stack:** PostgreSQL, pgvector, TypeScript

### Auth Service Component
**Responsibility:** 处理用户认证和授权

**Key Interfaces:**
- Anonymous auth
- Phone auth
- JWT validation
- Session management

**Dependencies:** Supabase Auth

**Technology Stack:** Supabase Auth, JWT

### Storage Service Component
**Responsibility:** 管理文件存储（语音、图片等）

**Key Interfaces:**
- File upload/download
- Presigned URLs
- File encryption

**Dependencies:** Supabase Storage

**Technology Stack:** Supabase Storage, S3 compatible API

### Component Interaction Diagram

```mermaid
graph LR
    subgraph "Mobile App"
        UI[UI Layer]
        State[State Management]
        Service[Service Layer]
        Cache[Local Cache]
    end
    
    subgraph "Vercel Edge"
        API[API Gateway]
        Chat[Chat Service]
        Memory[Memory Service]
    end
    
    subgraph "External Services"
        OpenAI[OpenAI API]
        Supabase[Supabase]
    end
    
    UI --> State
    State --> Service
    Service --> Cache
    Service --> API
    
    API --> Chat
    API --> Memory
    
    Chat --> OpenAI
    Chat --> Supabase
    Memory --> Supabase
```

---

## External APIs

### OpenAI API
- **Purpose:** 提供AI对话能力和文本嵌入
- **Documentation:** https://platform.openai.com/docs
- **Base URL(s):** https://api.openai.com/v1
- **Authentication:** Bearer token (API Key)
- **Rate Limits:** 
  - GPT-4: 10,000 TPM (tokens per minute)
  - Embeddings: 1,000,000 TPM

**Key Endpoints Used:**
- `POST /chat/completions` - 生成对话回复
- `POST /embeddings` - 生成文本向量

**Integration Notes:** 
- 使用流式响应减少延迟
- 实现重试机制处理限流
- 缓存常见问题的回复

### Supabase API
- **Purpose:** 提供数据库、认证、存储服务
- **Documentation:** https://supabase.com/docs
- **Base URL(s):** https://[project-id].supabase.co
- **Authentication:** API Key + JWT
- **Rate Limits:** 根据套餐不同

**Key Endpoints Used:**
- `POST /auth/v1/signup` - 用户注册
- `POST /rest/v1/messages` - 消息CRUD
- `GET /realtime/v1/websocket` - 实时订阅

**Integration Notes:**
- 使用Row Level Security保护数据
- 启用实时订阅减少轮询

---

## Core Workflows

### User Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Vercel
    participant Supabase
    
    User->>App: 打开应用
    App->>App: 检查本地token
    
    alt 无token或已过期
        App->>User: 显示欢迎页
        User->>App: 选择匿名登录
        App->>Vercel: POST /api/auth/anonymous
        Vercel->>Supabase: 创建匿名用户
        Supabase-->>Vercel: 返回用户信息
        Vercel-->>App: 返回token
        App->>App: 存储token
    else token有效
        App->>Vercel: 验证token
        Vercel->>Supabase: 验证session
        Supabase-->>Vercel: session有效
        Vercel-->>App: 认证成功
    end
    
    App->>User: 进入主界面
```

### Message Send and Response Flow

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Cache
    participant API
    participant Memory
    participant OpenAI
    participant DB
    
    User->>App: 输入消息
    App->>App: 本地加密
    App->>API: POST /api/chat/send
    
    API->>Cache: 检查会话缓存
    API->>Memory: 检索相关记忆
    Memory->>DB: 向量搜索
    DB-->>Memory: 返回相关记忆
    Memory-->>API: 记忆上下文
    
    API->>OpenAI: 调用GPT-4 (流式)
    OpenAI-->>API: Stream chunks
    API-->>App: SSE推送
    App-->>User: 逐字显示
    
    API->>DB: 保存消息
    API->>Memory: 提取并保存记忆
    API->>Cache: 更新缓存
```

### Memory Storage and Retrieval Flow

```mermaid
sequenceDiagram
    participant Chat
    participant Memory
    participant Embedding
    participant Vector
    participant Cache
    
    Note over Chat,Memory: 存储流程
    Chat->>Memory: 新消息内容
    Memory->>Embedding: 生成嵌入向量
    Embedding->>OpenAI: text-embedding-3
    OpenAI-->>Embedding: 1536维向量
    Embedding-->>Memory: 向量结果
    Memory->>Vector: 存储到pgvector
    Memory->>Cache: 更新热数据缓存
    
    Note over Chat,Memory: 检索流程
    Chat->>Memory: 查询请求
    Memory->>Cache: 检查缓存
    
    alt 缓存未命中
        Memory->>Embedding: 查询向量化
        Embedding-->>Memory: 查询向量
        Memory->>Vector: 相似度搜索
        Vector-->>Memory: Top-K结果
        Memory->>Cache: 更新缓存
    end
    
    Memory-->>Chat: 相关记忆
```

---

## Database Schema

### PostgreSQL Schema Design

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    phone VARCHAR(20) UNIQUE,
    nickname VARCHAR(50),
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    is_anonymous BOOLEAN DEFAULT false,
    last_seen_at TIMESTAMPTZ DEFAULT NOW()
);

-- Conversations table
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    is_archived BOOLEAN DEFAULT false,
    
    INDEX idx_conversations_user_id (user_id),
    INDEX idx_conversations_updated_at (updated_at)
);

-- Messages table (with encryption)
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL, -- Encrypted content
    created_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    INDEX idx_messages_conversation (conversation_id),
    INDEX idx_messages_created_at (created_at)
);

-- Memories table with vector
CREATE TABLE memories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('fact', 'preference', 'event', 'emotion')),
    content TEXT NOT NULL,
    embedding VECTOR(1536), -- OpenAI embedding dimension
    importance FLOAT DEFAULT 0.5 CHECK (importance >= 0 AND importance <= 1),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    accessed_at TIMESTAMPTZ DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    
    INDEX idx_memories_user_id (user_id),
    INDEX idx_memories_type (type),
    INDEX idx_memories_importance (importance DESC)
);

-- Create vector similarity search index
CREATE INDEX memories_embedding_idx ON memories 
USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Mood check-ins table
CREATE TABLE mood_checkins (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    mood_value INTEGER NOT NULL CHECK (mood_value >= 0 AND mood_value <= 100),
    mood_label VARCHAR(50) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    note TEXT,
    
    INDEX idx_mood_user_id (user_id),
    INDEX idx_mood_created_at (created_at)
);

-- Row Level Security Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_checkins ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" 
    ON users FOR SELECT 
    USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" 
    ON users FOR UPDATE 
    USING (auth.uid() = id);

CREATE POLICY "Users can view own conversations" 
    ON conversations FOR ALL 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can view own messages" 
    ON messages FOR ALL 
    USING (
        conversation_id IN (
            SELECT id FROM conversations WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage own memories" 
    ON memories FOR ALL 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own mood checkins" 
    ON mood_checkins FOR ALL 
    USING (auth.uid() = user_id);

-- Functions for common operations
CREATE OR REPLACE FUNCTION search_memories(
    query_embedding VECTOR(1536),
    match_count INT DEFAULT 5,
    user_uuid UUID DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    type VARCHAR,
    importance FLOAT,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        m.id,
        m.content,
        m.type,
        m.importance,
        1 - (m.embedding <=> query_embedding) AS similarity
    FROM memories m
    WHERE (user_uuid IS NULL OR m.user_id = user_uuid)
    ORDER BY m.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;
```

---

## Frontend Architecture

### Component Architecture

#### Component Organization
```
apps/mobile/src/
├── components/
│   ├── core/              # 核心UI组件
│   │   ├── ChatBubble.tsx
│   │   ├── EmotionalLoader.tsx
│   │   ├── MoodCheckIn.tsx
│   │   └── EmptyState.tsx
│   ├── screens/           # 页面组件
│   │   ├── ChatScreen.tsx
│   │   ├── OnboardingScreen.tsx
│   │   └── SettingsScreen.tsx
│   └── shared/            # 共享组件
├── hooks/                 # 自定义Hooks
│   ├── useAuth.ts
│   ├── useChat.ts
│   └── useMemory.ts
├── services/              # API服务层
│   ├── api.ts
│   ├── auth.service.ts
│   └── chat.service.ts
├── stores/               # Zustand状态管理
│   ├── authStore.ts
│   ├── chatStore.ts
│   └── settingsStore.ts
├── utils/                # 工具函数
│   ├── encryption.ts
│   └── storage.ts
└── theme/               # 主题配置
```

#### Component Template
```typescript
// 标准组件模板
import React, { FC, memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '@/hooks/useTheme';

interface ComponentProps {
  // Props definition
}

export const Component: FC<ComponentProps> = memo(({ ...props }) => {
  const theme = useTheme();
  
  // Component logic
  
  return (
    <View style={styles.container}>
      {/* Component JSX */}
    </View>
  );
});

Component.displayName = 'Component';

const styles = StyleSheet.create({
  container: {
    // Styles
  }
});
```

### State Management Architecture

#### State Structure
```typescript
// stores/types.ts
interface AppState {
  auth: {
    user: User | null;
    token: string | null;
    isLoading: boolean;
  };
  chat: {
    conversations: Conversation[];
    currentConversation: Conversation | null;
    messages: Message[];
    isTyping: boolean;
  };
  memory: {
    memories: Memory[];
    searchResults: Memory[];
  };
  ui: {
    theme: 'light' | 'dark' | 'auto';
    isOnboarding: boolean;
    activeModal: string | null;
  };
}
```

#### State Management Patterns
- 使用Zustand的slice模式组织store
- 实现optimistic updates提升体验
- 使用immer处理不可变更新
- 持久化关键状态到MMKV
- 实现状态同步中间件

### Routing Architecture

#### Route Organization
```
src/navigation/
├── RootNavigator.tsx      # 根导航器
├── AuthNavigator.tsx      # 认证流程
├── MainNavigator.tsx      # 主应用导航
└── types.ts              # 路由类型定义
```

#### Protected Route Pattern
```typescript
// navigation/ProtectedRoute.tsx
export const ProtectedRoute: FC<Props> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();
  
  if (isLoading) {
    return <SplashScreen />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/onboarding" />;
  }
  
  return children;
};
```

### Frontend Services Layer

#### API Client Setup
```typescript
// services/api.ts
import axios from 'axios';
import { MMKV } from 'react-native-mmkv';

const storage = new MMKV();

export const apiClient = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  timeout: 10000,
});

// Request interceptor
apiClient.interceptors.request.use((config) => {
  const token = storage.getString('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor with retry logic
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh
      await refreshToken();
      return apiClient.request(error.config);
    }
    return Promise.reject(error);
  }
);
```

#### Service Example
```typescript
// services/chat.service.ts
export class ChatService {
  async sendMessage(content: string): Promise<void> {
    const encrypted = await encrypt(content);
    
    const response = await apiClient.post('/chat/send', {
      message: encrypted,
      conversationId: getCurrentConversationId(),
    });
    
    // Handle streaming response
    const reader = response.body.getReader();
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      // Process chunk
      const text = new TextDecoder().decode(value);
      chatStore.appendMessage(text);
    }
  }
  
  async getConversations(): Promise<Conversation[]> {
    const { data } = await apiClient.get('/chat/conversations');
    return data;
  }
}
```

---

## Backend Architecture

### Service Architecture

#### Function Organization
```
packages/api/
├── functions/
│   ├── auth/
│   │   ├── anonymous.ts
│   │   └── phone.ts
│   ├── chat/
│   │   ├── send.ts
│   │   ├── conversations.ts
│   │   └── messages.ts
│   ├── memory/
│   │   ├── search.ts
│   │   └── save.ts
│   └── mood/
│       ├── checkin.ts
│       └── history.ts
├── lib/
│   ├── supabase.ts
│   ├── openai.ts
│   └── redis.ts
├── middleware/
│   ├── auth.ts
│   ├── cors.ts
│   └── rateLimit.ts
└── utils/
    ├── encryption.ts
    └── validation.ts
```

#### Function Template
```typescript
// functions/chat/send.ts
import { VercelRequest, VercelResponse } from '@vercel/node';
import { withAuth } from '@/middleware/auth';
import { withRateLimit } from '@/middleware/rateLimit';
import { openai } from '@/lib/openai';
import { supabase } from '@/lib/supabase';

async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable SSE
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  
  try {
    const { message, conversationId } = req.body;
    
    // Get relevant memories
    const memories = await searchMemories(req.userId, message);
    
    // Create completion stream
    const stream = await openai.chat.completions.create({
      model: 'gpt-4-turbo-preview',
      messages: buildPrompt(message, memories),
      stream: true,
    });
    
    // Stream response
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        res.write(`data: ${JSON.stringify({ content })}\n\n`);
      }
    }
    
    res.write('data: [DONE]\n\n');
    res.end();
  } catch (error) {
    console.error('Chat error:', error);
    res.write(`data: ${JSON.stringify({ error: 'Internal error' })}\n\n`);
    res.end();
  }
}

export default withAuth(withRateLimit(handler));
```

### Database Architecture

#### Data Access Layer
```typescript
// packages/api/lib/repositories/memory.repository.ts
export class MemoryRepository {
  async search(userId: string, query: string, limit = 5): Promise<Memory[]> {
    // Generate embedding
    const { data: embedding } = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: query,
    });
    
    // Vector search
    const { data, error } = await supabase.rpc('search_memories', {
      query_embedding: embedding[0].embedding,
      match_count: limit,
      user_uuid: userId,
    });
    
    if (error) throw error;
    return data;
  }
  
  async save(userId: string, memory: Partial<Memory>): Promise<Memory> {
    // Generate embedding
    const { data: embedding } = await openai.embeddings.create({
      model: 'text-embedding-3-small',
      input: memory.content,
    });
    
    // Save to database
    const { data, error } = await supabase
      .from('memories')
      .insert({
        ...memory,
        user_id: userId,
        embedding: embedding[0].embedding,
      })
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
}
```

### Authentication and Authorization

#### Auth Flow Diagram
```mermaid
sequenceDiagram
    participant Client
    participant Edge
    participant Supabase
    participant JWT
    
    Client->>Edge: Request with token
    Edge->>JWT: Verify token
    JWT-->>Edge: Token valid
    Edge->>Supabase: Get user session
    Supabase-->>Edge: Session data
    Edge->>Edge: Process request
    Edge-->>Client: Response
```

#### Middleware/Guards
```typescript
// middleware/auth.ts
import jwt from 'jsonwebtoken';
import { supabase } from '@/lib/supabase';

export function withAuth(handler: Handler) {
  return async (req: VercelRequest, res: VercelResponse) => {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }
    
    try {
      // Verify JWT
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Verify session with Supabase
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (error || !user) {
        throw new Error('Invalid session');
      }
      
      // Add user to request
      req.userId = user.id;
      
      // Continue to handler
      return handler(req, res);
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token' });
    }
  };
}
```

---

## Unified Project Structure

```plaintext
her/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml            # 测试和检查
│       └── deploy.yaml        # 部署流程
├── apps/                       # 应用程序包
│   ├── mobile/                # React Native应用
│   │   ├── src/
│   │   │   ├── components/   # UI组件
│   │   │   ├── screens/      # 页面组件
│   │   │   ├── hooks/        # 自定义Hooks
│   │   │   ├── services/     # API服务
│   │   │   ├── stores/       # 状态管理
│   │   │   ├── utils/        # 工具函数
│   │   │   └── theme/        # 主题配置
│   │   ├── android/          # Android配置
│   │   ├── ios/              # iOS配置
│   │   ├── app.json          # Expo配置
│   │   └── package.json
│   └── web/                   # 管理后台（可选）
│       ├── src/
│       └── package.json
├── packages/                   # 共享包
│   ├── api/                   # Vercel Functions
│   │   ├── functions/         # API函数
│   │   ├── lib/              # 库文件
│   │   ├── middleware/       # 中间件
│   │   ├── utils/            # 工具
│   │   └── package.json
│   ├── shared/                # 共享类型和工具
│   │   ├── src/
│   │   │   ├── types/        # TypeScript接口
│   │   │   ├── constants/    # 共享常量
│   │   │   └── utils/        # 共享工具
│   │   └── package.json
│   └── ui/                    # 共享UI组件
│       ├── src/
│       └── package.json
├── infrastructure/            # 基础设施配置
│   ├── terraform/            # Terraform配置
│   └── docker/              # Docker配置
├── scripts/                   # 构建和部署脚本
│   ├── setup.sh             # 初始化脚本
│   └── deploy.sh            # 部署脚本
├── docs/                      # 文档
│   ├── prd.md               # 产品需求文档
│   ├── architecture.md      # 架构文档
│   └── design/              # 设计文档
├── .env.example               # 环境变量模板
├── package.json               # 根package.json
├── pnpm-workspace.yaml        # pnpm工作区配置
├── tsconfig.json             # TypeScript配置
└── README.md                 # 项目说明
```

---

## Development Workflow

### Local Development Setup

#### Prerequisites
```bash
# 系统要求
Node.js 20 LTS
pnpm 8+
Git
iOS: Xcode 14+ (Mac only)
Android: Android Studio

# 全局工具
npm install -g pnpm
npm install -g expo-cli
npm install -g vercel
```

#### Initial Setup
```bash
# 克隆仓库
git clone https://github.com/your-org/her.git
cd her

# 安装依赖
pnpm install

# 设置环境变量
cp .env.example .env.local
# 编辑 .env.local 填入必要的API密钥

# 初始化Supabase（本地开发）
pnpm supabase init
pnpm supabase start

# iOS准备（仅Mac）
cd apps/mobile/ios && pod install
```

#### Development Commands
```bash
# 启动所有服务
pnpm dev

# 启动移动端开发
pnpm --filter mobile dev

# 启动API开发服务器
pnpm --filter api dev

# 运行测试
pnpm test

# 类型检查
pnpm typecheck

# 代码检查
pnpm lint
```

### Environment Configuration

#### Required Environment Variables
```bash
# Frontend (.env.local in apps/mobile)
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=xxx

# Backend (.env in packages/api)
OPENAI_API_KEY=sk-xxx
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_KEY=xxx
JWT_SECRET=xxx
KV_REST_API_URL=https://xxx.vercel-storage.com
KV_REST_API_TOKEN=xxx

# Shared
NODE_ENV=development
LOG_LEVEL=debug
```

---

## Deployment Architecture

### Deployment Strategy

**Frontend Deployment:**
- **Platform:** Expo EAS Build + Update
- **Build Command:** `eas build --platform all`
- **Output Directory:** Managed by EAS
- **CDN/Edge:** Cloudflare

**Backend Deployment:**
- **Platform:** Vercel
- **Build Command:** `vercel build`
- **Deployment Method:** Git push to main branch

### CI/CD Pipeline

```yaml
# .github/workflows/deploy.yaml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'
      - run: pnpm install
      - run: pnpm test
      - run: pnpm typecheck

  deploy-api:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

  deploy-mobile:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: expo/expo-github-action@v8
      - run: eas build --platform all --non-interactive
      - run: eas submit --platform all
```

### Environments

| Environment | Frontend URL | Backend URL | Purpose |
|------------|--------------|-------------|---------|
| Development | http://localhost:8081 | http://localhost:3000/api | 本地开发 |
| Staging | https://staging.her-app.com | https://staging-api.her-app.com | 预发布测试 |
| Production | https://her-app.com | https://api.her-app.com | 生产环境 |

---

## Security and Performance

### Security Requirements

**Frontend Security:**
- CSP Headers: `default-src 'self'; script-src 'self' 'unsafe-inline';`
- XSS Prevention: 所有用户输入进行sanitize
- Secure Storage: 使用MMKV加密存储敏感数据

**Backend Security:**
- Input Validation: 使用Zod验证所有输入
- Rate Limiting: 每IP每分钟60请求
- CORS Policy: 仅允许官方域名

**Authentication Security:**
- Token Storage: Secure Keychain (iOS) / Keystore (Android)
- Session Management: 30天过期，可刷新
- Password Policy: 不适用（使用手机验证码）

### Performance Optimization

**Frontend Performance:**
- Bundle Size Target: < 5MB initial load
- Loading Strategy: 懒加载非关键组件
- Caching Strategy: MMKV缓存最近100条消息

**Backend Performance:**
- Response Time Target: P95 < 2秒
- Database Optimization: 使用索引，定期VACUUM
- Caching Strategy: Redis缓存热数据，TTL 5分钟

---

## Testing Strategy

### Testing Pyramid
```
        E2E Tests (10%)
       /              \
    Integration Tests (30%)
    /                    \
Frontend Unit (30%)  Backend Unit (30%)
```

### Test Organization

#### Frontend Tests
```
apps/mobile/src/
├── __tests__/
│   ├── components/    # 组件单元测试
│   ├── screens/       # 页面测试
│   └── services/      # 服务测试
```

#### Backend Tests
```
packages/api/
├── __tests__/
│   ├── functions/     # 函数单元测试
│   ├── integration/   # 集成测试
│   └── e2e/          # 端到端测试
```

### Test Examples

#### Frontend Component Test
```typescript
// __tests__/components/ChatBubble.test.tsx
import { render, screen } from '@testing-library/react-native';
import { ChatBubble } from '@/components/ChatBubble';

describe('ChatBubble', () => {
  it('renders message correctly', () => {
    render(
      <ChatBubble
        type="user"
        message="Hello"
        timestamp="10:00"
      />
    );
    
    expect(screen.getByText('Hello')).toBeTruthy();
    expect(screen.getByText('10:00')).toBeTruthy();
  });
});
```

#### Backend API Test
```typescript
// __tests__/functions/chat.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/functions/chat/send';

describe('Chat Send API', () => {
  it('returns streaming response', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        message: 'Hello',
        conversationId: 'test-id',
      },
    });
    
    await handler(req, res);
    
    expect(res._getHeaders()['content-type']).toBe('text/event-stream');
  });
});
```

---

## Coding Standards

### Critical Fullstack Rules
- **Type Sharing:** 所有类型定义在packages/shared，从那里导入
- **API Calls:** 永远不要直接HTTP调用 - 使用service层
- **Environment Variables:** 只通过config对象访问，永不直接使用process.env
- **Error Handling:** 所有API路由必须使用标准错误处理器
- **State Updates:** 永不直接修改状态 - 使用正确的状态管理模式
- **Encryption:** 所有敏感数据必须加密后存储
- **Logging:** 使用结构化日志，不要console.log

### Naming Conventions

| Element | Frontend | Backend | Example |
|---------|----------|---------|---------|
| Components | PascalCase | - | `ChatBubble.tsx` |
| Hooks | camelCase with 'use' | - | `useAuth.ts` |
| API Routes | - | kebab-case | `/api/chat-send` |
| Database Tables | - | snake_case | `mood_checkins` |
| Constants | UPPER_SNAKE_CASE | UPPER_SNAKE_CASE | `MAX_MESSAGE_LENGTH` |

---

## Error Handling Strategy

### Error Flow Diagram
```mermaid
sequenceDiagram
    participant User
    participant App
    participant API
    participant Sentry
    
    User->>App: 触发错误
    App->>App: 本地错误处理
    App->>API: 如果是API错误
    API->>API: 错误处理中间件
    API-->>App: 标准错误响应
    App->>Sentry: 记录错误
    App->>User: 显示友好提示
```

### Error Response Format
```typescript
interface ApiError {
  error: {
    code: string;      // 错误代码
    message: string;   // 用户友好消息
    details?: any;     // 详细信息
    timestamp: string; // 时间戳
    requestId: string; // 请求ID
  };
}
```

### Frontend Error Handling
```typescript
// utils/errorHandler.ts
export class ErrorHandler {
  static handle(error: any): void {
    if (error.response) {
      // API错误
      this.handleApiError(error.response);
    } else if (error.request) {
      // 网络错误
      this.handleNetworkError();
    } else {
      // 其他错误
      this.handleGenericError(error);
    }
    
    // 记录到Sentry
    Sentry.captureException(error);
  }
  
  static handleApiError(response: any): void {
    const { code, message } = response.data.error;
    
    switch (code) {
      case 'AUTH_FAILED':
        // 重新登录
        authStore.logout();
        break;
      case 'RATE_LIMITED':
        // 显示限流提示
        Toast.show('请稍后再试');
        break;
      default:
        Toast.show(message);
    }
  }
}
```

### Backend Error Handling
```typescript
// middleware/errorHandler.ts
export function errorHandler(
  err: any,
  req: VercelRequest,
  res: VercelResponse
) {
  const requestId = generateRequestId();
  
  // 记录错误
  logger.error({
    requestId,
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
  });
  
  // 返回标准错误响应
  const statusCode = err.statusCode || 500;
  const code = err.code || 'INTERNAL_ERROR';
  const message = err.expose ? err.message : '服务器错误';
  
  res.status(statusCode).json({
    error: {
      code,
      message,
      timestamp: new Date().toISOString(),
      requestId,
    },
  });
}
```

---

## Monitoring and Observability

### Monitoring Stack
- **Frontend Monitoring:** Sentry for React Native
- **Backend Monitoring:** Vercel Analytics
- **Error Tracking:** Sentry
- **Performance Monitoring:** Vercel Web Vitals

### Key Metrics

**Frontend Metrics:**
- App启动时间
- 页面加载时间
- API响应时间
- 崩溃率
- 用户会话时长

**Backend Metrics:**
- 请求速率
- 错误率
- 响应时间 (P50, P95, P99)
- 数据库查询性能
- OpenAI API调用成本

---

## Checklist Results Report

架构设计已完成，待执行架构检查清单验证完整性。建议在实施前运行 `*execute-checklist` 命令进行全面检查。

---

_HER Architecture Document v1.0 - Designed for rapid deployment and global accessibility_