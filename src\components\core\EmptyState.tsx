/**
 * EmptyState Component
 * 空状态和对话开场白 - 让第一次对话自然开始
 */

import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
} from 'react-native';
import { defaultTheme, emotionalColors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing, borderRadius, shadows } from '../../theme/spacing';

const { width: screenWidth } = Dimensions.get('window');

interface ConversationStarter {
  id: string;
  text: string;
  icon?: string;
  mood?: 'neutral' | 'warm' | 'gentle' | 'encouraging';
}

interface EmptyStateProps {
  userName?: string;
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  showStarters?: boolean;
  onStarterPress?: (starter: ConversationStarter) => void;
  onInputFocus?: () => void;
  isFirstTime?: boolean;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  userName,
  timeOfDay,
  showStarters = true,
  onStarterPress,
  onInputFocus,
  isFirstTime = false,
}) => {
  const [currentGreeting, setCurrentGreeting] = useState('');
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const startersAnim = useRef(new Animated.Value(0)).current;

  // 获取时间相关的问候语
  const getGreeting = (): string => {
    const name = userName ? `，${userName}` : '';
    
    if (isFirstTime) {
      return `很高兴认识你${name}`;
    }

    const greetings = {
      morning: [
        `早安${name}`,
        `新的一天开始了${name}`,
        `早上好${name}，睡得还好吗？`,
      ],
      afternoon: [
        `午后时光${name}`,
        `下午好${name}`,
        `午休过了吗${name}？`,
      ],
      evening: [
        `晚上好${name}`,
        `晚饭吃了吗${name}？`,
        `今天辛苦了${name}`,
      ],
      night: [
        `夜深了${name}`,
        `还没睡吗${name}？`,
        `深夜好${name}，睡不着吗？`,
      ],
    };

    const options = greetings[timeOfDay];
    return options[Math.floor(Math.random() * options.length)];
  };

  // 获取对话开场白
  const getConversationStarters = (): ConversationStarter[] => {
    if (isFirstTime) {
      return [
        { id: '1', text: '想聊聊今天的心情', icon: '💭' },
        { id: '2', text: '有些事想说很久了', icon: '✨' },
        { id: '3', text: '就是想找个人说说话', icon: '💫' },
        { id: '4', text: '先随便聊聊吧', icon: '☕' },
      ];
    }

    const starters = {
      morning: [
        { id: '1', text: '昨晚做了个梦', icon: '💭', mood: 'neutral' },
        { id: '2', text: '今天有些焦虑', icon: '😟', mood: 'gentle' },
        { id: '3', text: '期待今天的安排', icon: '✨', mood: 'warm' },
        { id: '4', text: '想聊聊最近的事', icon: '💬', mood: 'neutral' },
      ],
      afternoon: [
        { id: '1', text: '工作有点累', icon: '😮‍💨', mood: 'gentle' },
        { id: '2', text: '午后想放空一下', icon: '☁️', mood: 'neutral' },
        { id: '3', text: '发生了有趣的事', icon: '😊', mood: 'warm' },
        { id: '4', text: '需要一些鼓励', icon: '💪', mood: 'encouraging' },
      ],
      evening: [
        { id: '1', text: '今天过得好快', icon: '⏰', mood: 'neutral' },
        { id: '2', text: '想分享今天的事', icon: '📝', mood: 'warm' },
        { id: '3', text: '有些困惑想倾诉', icon: '🤔', mood: 'gentle' },
        { id: '4', text: '感觉有点孤单', icon: '🌙', mood: 'gentle' },
      ],
      night: [
        { id: '1', text: '睡不着，想聊天', icon: '🌙', mood: 'gentle' },
        { id: '2', text: '夜深人静想很多', icon: '💭', mood: 'neutral' },
        { id: '3', text: '有心事睡不着', icon: '😔', mood: 'gentle' },
        { id: '4', text: '就是想有人陪', icon: '✨', mood: 'warm' },
      ],
    };

    return starters[timeOfDay];
  };

  // 动画效果
  useEffect(() => {
    // 问候语淡入
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // HER存在感脉动
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // 延迟显示对话建议
    if (showStarters) {
      setTimeout(() => {
        Animated.spring(startersAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }).start();
      }, 1500);
    }
  }, []);

  useEffect(() => {
    setCurrentGreeting(getGreeting());
  }, [timeOfDay, userName, isFirstTime]);

  // 渲染HER的存在感指示器
  const renderPresenceIndicator = () => (
    <Animated.View
      style={[
        styles.presenceIndicator,
        {
          transform: [{ scale: pulseAnim }],
          opacity: pulseAnim.interpolate({
            inputRange: [1, 1.05],
            outputRange: [0.6, 0.3],
          }),
        },
      ]}
    >
      <View style={styles.presenceCore} />
    </Animated.View>
  );

  // 渲染对话开场白卡片
  const renderStarter = (starter: ConversationStarter) => {
    const moodColors = {
      neutral: defaultTheme.bubble.her,
      warm: emotionalColors.caring.bubble,
      gentle: emotionalColors.listening.bubble,
      encouraging: emotionalColors.understanding.bubble,
    };

    return (
      <TouchableOpacity
        key={starter.id}
        style={[
          styles.starterCard,
          { backgroundColor: moodColors[starter.mood || 'neutral'] },
        ]}
        onPress={() => onStarterPress?.(starter)}
        activeOpacity={0.7}
      >
        <Text style={styles.starterIcon}>{starter.icon}</Text>
        <Text style={styles.starterText}>{starter.text}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {/* HER的存在感 */}
      {renderPresenceIndicator()}
      
      {/* 问候语 */}
      <Animated.Text
        style={[
          styles.greeting,
          {
            opacity: fadeAnim,
            transform: [{
              translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [10, 0],
              }),
            }],
          },
        ]}
      >
        {currentGreeting}
      </Animated.Text>

      {/* 副标题 */}
      <Animated.Text
        style={[
          styles.subtitle,
          {
            opacity: fadeAnim,
            transform: [{
              translateY: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [10, 0],
              }),
            }],
          },
        ]}
      >
        {isFirstTime ? '在这里，你可以说任何想说的话' : '我在这里陪着你'}
      </Animated.Text>

      {/* 输入提示 */}
      <TouchableOpacity
        style={styles.inputHint}
        onPress={onInputFocus}
        activeOpacity={0.6}
      >
        <Text style={styles.inputHintText}>
          {isFirstTime ? '点击这里开始倾诉...' : '想说些什么...'}
        </Text>
      </TouchableOpacity>

      {/* 对话开场白建议 */}
      {showStarters && (
        <Animated.View
          style={[
            styles.startersContainer,
            {
              opacity: startersAnim,
              transform: [{
                scale: startersAnim,
              }],
            },
          ]}
        >
          <Text style={styles.startersTitle}>或者选择一个话题</Text>
          <View style={styles.startersGrid}>
            {getConversationStarters().map(renderStarter)}
          </View>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
  },
  
  // 存在感指示器
  presenceIndicator: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: defaultTheme.bubble.her,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xl,
  },
  
  presenceCore: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: defaultTheme.accent,
    opacity: 0.8,
  },
  
  // 问候语
  greeting: {
    ...textStyles.title1,
    color: defaultTheme.text.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  
  subtitle: {
    ...textStyles.body,
    color: defaultTheme.text.secondary,
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  
  // 输入提示
  inputHint: {
    width: '100%',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: defaultTheme.surface,
    borderRadius: borderRadius.pill,
    marginBottom: spacing.xl,
    ...shadows.sm,
  },
  
  inputHintText: {
    ...textStyles.chatHint,
    color: defaultTheme.text.secondary,
    textAlign: 'center',
  },
  
  // 对话开场白
  startersContainer: {
    width: '100%',
  },
  
  startersTitle: {
    ...textStyles.caption1,
    color: defaultTheme.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.md,
  },
  
  startersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: spacing.sm,
  },
  
  starterCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: borderRadius.pill,
    marginBottom: spacing.xs,
    ...shadows.xs,
  },
  
  starterIcon: {
    fontSize: 16,
    marginRight: spacing.xs,
  },
  
  starterText: {
    ...textStyles.footnote,
    color: defaultTheme.text.primary,
  },
});

export default EmptyState;