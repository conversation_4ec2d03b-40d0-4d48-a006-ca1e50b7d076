import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@her/ui';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

const EmotionalLoader = ({ emotion }: { emotion: 'listening' | 'thinking' | 'understanding' }) => {
  const theme = useTheme();
  const dots = [0, 1, 2];
  const animations = dots.map(() => useRef(new Animated.Value(0)).current);

  useEffect(() => {
    const animateDots = () => {
      animations.forEach((anim, index) => {
        Animated.loop(
          Animated.sequence([
            Animated.timing(anim, {
              toValue: 1,
              duration: 400,
              delay: index * 200,
              useNativeDriver: true,
            }),
            Animated.timing(anim, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ])
        ).start();
      });
    };
    animateDots();
  }, []);

  const getEmotionText = () => {
    switch (emotion) {
      case 'listening':
        return '正在倾听...';
      case 'thinking':
        return '思考中...';
      case 'understanding':
        return '理解中...';
    }
  };

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.twilightPink,
      borderRadius: 16,
      maxWidth: '75%',
    }}>
      <View style={{ flexDirection: 'row', marginRight: 8 }}>
        {animations.map((anim, index) => (
          <Animated.View
            key={index}
            style={{
              width: 8,
              height: 8,
              borderRadius: 4,
              backgroundColor: theme.colors.textSecondary,
              marginHorizontal: 2,
              opacity: anim,
              transform: [{
                scale: anim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [1, 1.3],
                }),
              }],
            }}
          />
        ))}
      </View>
      <Text style={{
        color: theme.colors.textSecondary,
        fontSize: 14,
      }}>
        {getEmotionText()}
      </Text>
    </View>
  );
};

const WelcomeInterface = () => {
  const theme = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();

    // Breathing animation for the heart
    Animated.loop(
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const getTimeGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return '早安，今天想聊些什么吗？';
    if (hour >= 12 && hour < 18) return '午后时光，需要休息一下吗？';
    if (hour >= 18 && hour < 22) return '晚上好，今天辛苦了';
    return '夜深了，睡不着吗？';
  };

  const conversationStarters = [
    { text: '今天的心情', emoji: '💭' },
    { text: '有些事想说', emoji: '✨' },
    { text: '就是想聊聊', emoji: '💫' },
    { text: '随便看看', emoji: '☕' },
  ];

  return (
    <Animated.View style={{
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      opacity: fadeAnim,
      transform: [{ scale: scaleAnim }],
      padding: 32,
    }}>
      <Animated.View style={{
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: theme.colors.twilightPink,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 24,
        transform: [{ scale: scaleAnim }],
      }}>
        <Ionicons name="heart" size={40} color={theme.colors.candleOrange} />
      </Animated.View>

      <Text style={{
        fontSize: 24,
        color: theme.colors.textPrimary,
        marginBottom: 8,
        textAlign: 'center',
      }}>
        {getTimeGreeting()}
      </Text>

      <Text style={{
        fontSize: 14,
        color: theme.colors.textSecondary,
        marginBottom: 32,
        textAlign: 'center',
        lineHeight: 20,
      }}>
        我在这里陪着你，有什么想说的都可以告诉我
      </Text>

      <View style={{
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'center',
        gap: 12,
      }}>
        {conversationStarters.map((starter, index) => (
          <TouchableOpacity
            key={index}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 10,
              borderRadius: 20,
              borderWidth: 1,
              borderColor: theme.colors.candleOrange,
              backgroundColor: 'transparent',
              margin: 4,
            }}
            activeOpacity={0.7}
          >
            <Text style={{
              color: theme.colors.textPrimary,
              fontSize: 14,
            }}>
              {starter.text} {starter.emoji}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Animated.View>
  );
};

const ChatBubble = ({ message }: { message: Message }) => {
  const theme = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(10)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={{
        alignSelf: message.isUser ? 'flex-end' : 'flex-start',
        maxWidth: '75%',
        marginVertical: 4,
        opacity: fadeAnim,
        transform: [{ translateY }],
      }}
    >
      <View
        style={{
          backgroundColor: message.isUser 
            ? theme.colors.moonlightBlue 
            : theme.colors.twilightPink,
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderRadius: 16,
          borderTopLeftRadius: message.isUser ? 16 : 8,
          borderTopRightRadius: message.isUser ? 8 : 16,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.08,
          shadowRadius: 4,
          elevation: 2,
        }}
      >
        <Text style={{
          color: theme.colors.textPrimary,
          fontSize: 16,
          lineHeight: 24,
        }}>
          {message.text}
        </Text>
        <Text style={{
          color: theme.colors.textSecondary,
          fontSize: 11,
          marginTop: 4,
          opacity: 0.7,
        }}>
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </Text>
      </View>
    </Animated.View>
  );
};

export default function ChatScreen() {
  const theme = useTheme();
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingEmotion, setLoadingEmotion] = useState<'listening' | 'thinking' | 'understanding'>('listening');
  const scrollViewRef = useRef<ScrollView>(null);
  const breathingAnim = useRef(new Animated.Value(1)).current;

  // Header breathing animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(breathingAnim, {
          toValue: 1.02,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(breathingAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const handleSendMessage = async () => {
    if (inputValue.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        text: inputValue,
        isUser: true,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, newMessage]);
      setInputValue('');

      // Simulate AI response with emotional loading
      setIsLoading(true);
      setLoadingEmotion('listening');

      setTimeout(() => setLoadingEmotion('thinking'), 800);
      setTimeout(() => setLoadingEmotion('understanding'), 1600);

      setTimeout(() => {
        const aiResponse: Message = {
          id: (Date.now() + 1).toString(),
          text: '我明白你的感受。谢谢你愿意与我分享这些。',
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiResponse]);
        setIsLoading(false);
      }, 2400);
    }
  };

  return (
    <SafeAreaView style={{ 
      flex: 1, 
      backgroundColor: theme.colors.morningCream 
    }}>
      {/* Header */}
      <View style={{
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.border,
        backgroundColor: theme.colors.morningCream,
      }}>
        <Animated.Text style={{
          fontSize: 24,
          fontWeight: '500',
          textAlign: 'center',
          color: theme.colors.textPrimary,
          transform: [{ scale: breathingAnim }],
        }}>
          HER
        </Animated.Text>
      </View>

      <KeyboardAvoidingView 
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}
      >
        {/* Messages Area */}
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={{
            flexGrow: 1,
            padding: 16,
          }}
          onContentSizeChange={() => {
            scrollViewRef.current?.scrollToEnd({ animated: true });
          }}
        >
          {messages.length === 0 ? (
            <WelcomeInterface />
          ) : (
            <>
              {messages.map(message => (
                <ChatBubble key={message.id} message={message} />
              ))}
              {isLoading && (
                <View style={{ marginVertical: 8 }}>
                  <EmotionalLoader emotion={loadingEmotion} />
                </View>
              )}
            </>
          )}
        </ScrollView>

        {/* Input Area */}
        <View style={{
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderTopWidth: 1,
          borderTopColor: theme.colors.border,
          backgroundColor: theme.colors.morningCream,
        }}>
          <View style={{
            flexDirection: 'row',
            alignItems: 'flex-end',
            gap: 12,
          }}>
            <View style={{
              flex: 1,
              backgroundColor: 'white',
              borderRadius: 24,
              borderWidth: 1,
              borderColor: theme.colors.border,
              paddingHorizontal: 16,
              minHeight: 44,
              maxHeight: 100,
            }}>
              <TextInput
                value={inputValue}
                onChangeText={setInputValue}
                placeholder="想说些什么..."
                placeholderTextColor={theme.colors.textSecondary}
                multiline
                style={{
                  fontSize: 16,
                  color: theme.colors.textPrimary,
                  paddingVertical: 10,
                  minHeight: 44,
                  maxHeight: 100,
                }}
                editable={!isLoading}
              />
            </View>

            <TouchableOpacity
              onPress={handleSendMessage}
              disabled={!inputValue.trim() || isLoading}
              style={{
                width: 44,
                height: 44,
                borderRadius: 22,
                backgroundColor: inputValue.trim() 
                  ? theme.colors.candleOrange 
                  : theme.colors.border,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              activeOpacity={0.7}
            >
              <Ionicons 
                name="send" 
                size={20} 
                color={inputValue.trim() ? 'white' : theme.colors.textSecondary} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}