{"name": "@her/shared", "version": "1.0.0", "private": true, "description": "HER共享类型定义和工具函数", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build", "dev": "tsc --build --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "test:ci": "jest --ci --coverage"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.10.6", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./constants": {"types": "./dist/constants/index.d.ts", "default": "./dist/constants/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}}}