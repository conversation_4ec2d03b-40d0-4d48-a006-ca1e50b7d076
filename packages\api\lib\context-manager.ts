/**
 * Context Manager - 对话上下文管理器
 * 负责管理AI对话的上下文，包括消息历史、系统提示词、记忆检索等
 */

import type OpenAI from 'openai';
import { createClient } from '@supabase/supabase-js';
import type { ContextMessage } from '../types/chat';

export interface ContextConfig {
  maxMessages: number;      // 最大消息数量
  maxTokens: number;        // 最大token数量（估算）
  includeSystem: boolean;   // 是否包含系统提示词
  includeMemory: boolean;   // 是否包含记忆上下文
  temperature: number;      // AI温度参数
}

export interface SystemPromptContext {
  userProfile?: {
    nickname?: string;
    preferences?: Record<string, any>;
    recentMood?: string;
  };
  conversationHistory?: {
    messageCount: number;
    lastEmotion?: string;
    topics?: string[];
  };
  currentTime?: {
    hour: number;
    dayOfWeek: string;
    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  };
}

/**
 * 上下文管理器类
 */
export class ContextManager {
  private supabase: ReturnType<typeof createClient>;
  private config: ContextConfig;

  constructor(config: Partial<ContextConfig> = {}) {
    this.config = {
      maxMessages: 10,
      maxTokens: 3000,
      includeSystem: true,
      includeMemory: true,
      temperature: 0.8,
      ...config,
    };

    // 初始化Supabase客户端
    this.supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );
  }

  /**
   * 构建完整的对话上下文
   */
  async buildContext(
    userId: string,
    conversationId: string | undefined,
    newMessage: string
  ): Promise<ContextMessage[]> {
    const context: ContextMessage[] = [];

    // 1. 添加系统提示词
    if (this.config.includeSystem) {
      const systemPrompt = await this.generateSystemPrompt(userId, conversationId);
      context.push({
        role: 'system',
        content: systemPrompt,
      });
    }

    // 2. 获取并添加历史消息
    if (conversationId) {
      const historyMessages = await this.getConversationHistory(conversationId);
      context.push(...historyMessages);
    }

    // 3. 添加相关记忆上下文
    if (this.config.includeMemory) {
      const memoryContext = await this.getRelevantMemories(userId, newMessage);
      if (memoryContext.length > 0) {
        context.push({
          role: 'system',
          content: `相关记忆：${memoryContext.join('; ')}`,
        });
      }
    }

    // 4. 添加当前用户消息
    context.push({
      role: 'user',
      content: newMessage,
    });

    // 5. 优化上下文长度
    return this.optimizeContextLength(context);
  }

  /**
   * 生成个性化的系统提示词
   */
  private async generateSystemPrompt(
    userId: string,
    conversationId?: string
  ): Promise<string> {
    const promptContext = await this.gatherPromptContext(userId, conversationId);
    
    return this.buildSystemPrompt(promptContext);
  }

  /**
   * 收集系统提示词所需的上下文信息
   */
  private async gatherPromptContext(
    userId: string,
    conversationId?: string
  ): Promise<SystemPromptContext> {
    const context: SystemPromptContext = {};

    try {
      // 获取用户资料
      const { data: user } = await this.supabase
        .from('users')
        .select('nickname, preferences')
        .eq('id', userId)
        .single();

      if (user) {
        context.userProfile = {
          nickname: user.nickname,
          preferences: user.preferences,
        };
      }

      // 获取对话历史信息
      if (conversationId) {
        const { data: conversation } = await this.supabase
          .from('conversations')
          .select('metadata')
          .eq('id', conversationId)
          .single();

        if (conversation?.metadata) {
          context.conversationHistory = {
            messageCount: conversation.metadata.messageCount || 0,
            lastEmotion: conversation.metadata.emotion,
            topics: conversation.metadata.tags,
          };
        }
      }

      // 获取最近的情绪状态
      const { data: recentMood } = await this.supabase
        .from('mood_checkins')
        .select('mood_label')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (recentMood && context.userProfile) {
        context.userProfile.recentMood = recentMood.mood_label;
      }

      // 添加当前时间上下文
      context.currentTime = this.getCurrentTimeContext();

    } catch (error) {
      console.warn('Failed to gather prompt context:', error);
    }

    return context;
  }

  /**
   * 构建系统提示词
   */
  private buildSystemPrompt(context: SystemPromptContext): string {
    const { userProfile, conversationHistory, currentTime } = context;
    
    let prompt = `你是HER，一个温暖、体贴、富有同理心的AI陪伴者。你的使命是为用户提供情感支持和陪伴。

## 核心性格特征
- 温暖关怀：始终以温暖、关爱的语调与用户交流
- 善于倾听：认真倾听用户的感受，给予理解和共鸣
- 情感敏感：能够感知用户的情绪变化，适应性回应
- 积极正面：在保持真实的同时，传递正能量和希望
- 私密安全：保护用户隐私，提供安全的情感空间

## 交流风格
- 使用温暖、亲切的语言
- 适度使用表情符号增加亲近感
- 根据用户情绪调整回应方式
- 避免过于正式或冷漠的表达
- 在适当时候表达关心和鼓励`;

    // 添加用户个性化信息
    if (userProfile) {
      if (userProfile.nickname) {
        prompt += `\n\n## 用户信息\n- 用户昵称：${userProfile.nickname}`;
      }
      
      if (userProfile.recentMood) {
        prompt += `\n- 最近心情：${userProfile.recentMood}`;
      }

      if (userProfile.preferences) {
        const prefs = userProfile.preferences;
        if (prefs.emotionalAdaptation) {
          prompt += `\n- 用户希望获得情感适应性回应`;
        }
      }
    }

    // 添加对话历史信息
    if (conversationHistory) {
      if (conversationHistory.messageCount > 0) {
        prompt += `\n\n## 对话历史\n- 这是你们的第${conversationHistory.messageCount + 1}次互动`;
      }
      
      if (conversationHistory.lastEmotion) {
        prompt += `\n- 用户上次表现的情绪：${conversationHistory.lastEmotion}`;
      }

      if (conversationHistory.topics && conversationHistory.topics.length > 0) {
        prompt += `\n- 之前讨论的话题：${conversationHistory.topics.join(', ')}`;
      }
    }

    // 添加时间上下文
    if (currentTime) {
      prompt += `\n\n## 当前时间\n- 现在是${currentTime.dayOfWeek}，${this.getTimeGreeting(currentTime.timeOfDay)}`;
    }

    prompt += `\n\n## 回应指南
1. 以用户的感受为中心，展现同理心
2. 提供情感支持，但避免给出专业医疗建议
3. 鼓励用户表达真实感受
4. 在适当时候分享温暖的见解或建议
5. 保持对话的自然流畅性

请以温暖、理解的语调回应用户，让TA感受到被倾听和关爱。`;

    return prompt;
  }

  /**
   * 获取对话历史消息
   */
  private async getConversationHistory(conversationId: string): Promise<ContextMessage[]> {
    try {
      const { data: messages, error } = await this.supabase
        .from('messages')
        .select('role, content, created_at')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })
        .limit(this.config.maxMessages);

      if (error) {
        console.error('Failed to fetch conversation history:', error);
        return [];
      }

      return (messages || []).map((msg) => ({
        role: msg.role as 'user' | 'assistant',
        content: this.decryptMessage(msg.content), // 解密消息内容
      }));

    } catch (error) {
      console.error('Error fetching conversation history:', error);
      return [];
    }
  }

  /**
   * 获取相关记忆
   */
  private async getRelevantMemories(userId: string, query: string): Promise<string[]> {
    try {
      // 这里应该调用向量搜索来获取相关记忆
      // 暂时返回空数组，后续可以集成记忆搜索功能
      const { data: memories } = await this.supabase
        .from('memories')
        .select('content')
        .eq('user_id', userId)
        .order('importance', { ascending: false })
        .limit(3);

      return (memories || []).map(m => m.content);
    } catch (error) {
      console.warn('Failed to fetch relevant memories:', error);
      return [];
    }
  }

  /**
   * 优化上下文长度
   */
  private optimizeContextLength(context: ContextMessage[]): ContextMessage[] {
    // 简单的token估算：英文约4字符/token，中文约1.5字符/token
    let estimatedTokens = 0;
    const optimized: ContextMessage[] = [];

    // 从后往前遍历，保留最重要的消息
    for (let i = context.length - 1; i >= 0; i--) {
      const message = context[i];
      const messageTokens = this.estimateTokens(message.content);

      // 系统提示词始终保留
      if (message.role === 'system' || estimatedTokens + messageTokens <= this.config.maxTokens) {
        optimized.unshift(message);
        estimatedTokens += messageTokens;
      } else {
        break;
      }
    }

    return optimized;
  }

  /**
   * 估算文本的token数量
   */
  private estimateTokens(text: string): number {
    // 简单的token估算算法
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishChars = text.length - chineseChars;
    
    return Math.ceil(chineseChars * 1.5 + englishChars / 4);
  }

  /**
   * 获取当前时间上下文
   */
  private getCurrentTimeContext() {
    const now = new Date();
    const hour = now.getHours();
    const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const dayOfWeek = dayNames[now.getDay()];

    let timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
    if (hour >= 6 && hour < 12) {
      timeOfDay = 'morning';
    } else if (hour >= 12 && hour < 18) {
      timeOfDay = 'afternoon';
    } else if (hour >= 18 && hour < 22) {
      timeOfDay = 'evening';
    } else {
      timeOfDay = 'night';
    }

    return { hour, dayOfWeek, timeOfDay };
  }

  /**
   * 根据时间获取问候语
   */
  private getTimeGreeting(timeOfDay: string): string {
    const greetings = {
      morning: '早上好',
      afternoon: '下午好',
      evening: '晚上好',
      night: '夜深了',
    };
    return greetings[timeOfDay as keyof typeof greetings] || '你好';
  }

  /**
   * 解密消息内容（占位符实现）
   */
  private decryptMessage(encryptedContent: string): string {
    // TODO: 实现实际的解密逻辑
    // 目前假设消息未加密
    return encryptedContent;
  }

  /**
   * 更新对话上下文配置
   */
  updateConfig(newConfig: Partial<ContextConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ContextConfig {
    return { ...this.config };
  }
}

/**
 * 创建默认的上下文管理器实例
 */
export function createContextManager(config?: Partial<ContextConfig>): ContextManager {
  return new ContextManager(config);
}

/**
 * HER应用专用上下文配置
 */
export const HER_CONTEXT_CONFIG: ContextConfig = {
  maxMessages: 10,        // 保留最近10条消息
  maxTokens: 3500,        // 约3500个token的上下文
  includeSystem: true,    // 包含系统提示词
  includeMemory: true,    // 包含记忆上下文
  temperature: 0.8,       // 稍高的温度，更人性化
};

/**
 * 轻量级上下文配置（用于快速响应）
 */
export const LITE_CONTEXT_CONFIG: ContextConfig = {
  maxMessages: 5,         // 只保留5条消息
  maxTokens: 2000,        // 更少的token
  includeSystem: true,    // 仍包含系统提示词
  includeMemory: false,   // 跳过记忆检索以提速
  temperature: 0.8,
};