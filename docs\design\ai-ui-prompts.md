# HER AI UI Generation Prompts
> 为v0、Lovable等AI工具准备的UI生成提示词

## Master Prompt: HER Chat Interface

### Complete Prompt for AI UI Generation

```prompt
# High-Level Goal
Create a warm, emotionally intelligent chat interface for HER, a private AI companion app built with React Native. The interface should feel like a "warm digital bedroom" - safe, private, and comforting. Focus on emotional design with breathing animations, gentle interactions, and time-aware themes.

# Project Context
- **Tech Stack**: React Native 0.73+, TypeScript 5, Zustand for state management
- **UI Framework**: Custom components with React Native Elements
- **Styling**: Inline styles with predefined theme system
- **Target**: iOS and Android mobile apps
- **Core Concept**: Private emotional companion, NOT a chatbot or assistant

# Design System Values
```typescript
// Color Palette - Use these EXACT values
const colors = {
  morningCream: '#FAF7F2',    // Main background
  twilightPink: '#F5E6E0',     // HER messages
  moonlightBlue: '#E8EEF5',    // User messages
  candleOrange: '#FFE5D4',     // Accent color
  textPrimary: '#262626',
  textSecondary: '#525252',
};

// Typography
const fonts = {
  chat: 'Nunito-Regular',      // Rounded, friendly
  ui: 'Inter-Regular',          // Clean UI text
  sizes: {
    message: 16,
    timestamp: 11,
    hint: 14,
  }
};

// Spacing (4px grid)
const spacing = {
  xs: 8, sm: 12, md: 16, lg: 24, xl: 32
};
```

# Detailed Step-by-Step Instructions

1. **Create the Main Chat Screen Component** (`ChatScreen.tsx`):
   - Use a SafeAreaView with morningCream background
   - Add a simple header with "HER" text centered, no back button
   - Include a subtle breathing animation (scale 1.0 to 1.02, 4s cycle)

2. **Build the Message List Component** (`MessageList.tsx`):
   - Use FlatList for performance with inverted={true}
   - Add 12px spacing between message groups
   - Group consecutive messages from same sender (8px gap)
   - Implement pull-to-load-more for history (no visible spinner)
   - Add a subtle bounce effect on scroll boundaries

3. **Design the Chat Bubble Component** (`ChatBubble.tsx`):
   - HER messages: Left-aligned, twilightPink background, max 75% width
   - User messages: Right-aligned, moonlightBlue background, max 75% width
   - Border radius: 16px, but 8px for consecutive same-sender corners
   - Add subtle shadow: 0 2px 8px rgba(0,0,0,0.08)
   - Entry animation: Fade in + slide up 10px over 300ms
   - Typography: 16px Nunito, 1.8 line height for readability

4. **Create the Emotional Loader Component** (`EmotionalLoader.tsx`):
   - Three states with different animations:
     a) "Listening" - Expanding ripples (3 circles, staggered)
     b) "Thinking" - Breathing glow (opacity 0.4 to 0.8)
     c) "Understanding" - Gentle pulse (scale 0.9 to 1.1)
   - Center in a bubble, twilightPink background
   - Show state text below: "正在倾听..." / "思考中..." / "理解中..."

5. **Build the Message Input Component** (`MessageInput.tsx`):
   - Pill-shaped container with 24px border radius
   - White background with subtle shadow
   - Placeholder text: "想说些什么..." in light gray
   - Auto-grow text input (max 4 lines)
   - Voice button on left (microphone icon)
   - Send button on right (paper plane icon)
   - Both buttons should have touch feedback (scale 0.96)

6. **Add the Empty State Component** (`EmptyState.tsx`):
   - Show when no messages exist
   - Breathing circle animation (80px diameter, twilightPink)
   - Greeting based on time:
     - Morning (6-12): "早安，今天想聊些什么吗？"
     - Afternoon (12-18): "午后时光，需要休息一下吗？"
     - Evening (18-22): "晚上好，今天辛苦了"
     - Night (22-6): "夜深了，睡不着吗？"
   - Show 4 conversation starter pills below:
     - "今天的心情 💭"
     - "有些事想说 ✨"
     - "就是想聊聊 💫"
     - "随便看看 ☕"

7. **Implement Mood Check-in Widget** (`MoodCheckIn.tsx`):
   - Optional slider that appears at conversation start
   - Visual gradient from sad (😔) to happy (😊)
   - No numbers, just emoji faces at endpoints
   - Slider thumb follows finger with haptic feedback
   - "今天感觉怎么样？" as prompt text
   - Skip button: "今天不想说"

8. **Create Time-Aware Theme System**:
   - Auto-detect device time and switch themes:
   - Morning: Bright morningCream background
   - Evening: Warmer with candleOrange tints
   - Night: Darker with reduced contrast (not pure black)
   - Smooth transition between themes (2s fade)

9. **Add Micro-interactions**:
   - Touch feedback: Scale 0.98 for 100ms on press
   - Message send: Subtle bounce animation
   - Typing indicator: Match user's typing rhythm
   - Long press on message: Gentle haptic + options menu
   - Pull down in empty chat: Elastic stretch effect

10. **Handle Edge Cases & States**:
    - Connection lost: Show inline notice, not modal
    - Message failed: Inline retry button on bubble
    - Loading history: Subtle top spinner
    - No more history: "这是我们的开始 ✨"

# Code Examples and Constraints

## Example Message Bubble Structure:
```tsx
<View style={[styles.bubble, isHer ? styles.herBubble : styles.userBubble]}>
  <Text style={styles.messageText}>{message.text}</Text>
  {showTimestamp && (
    <Text style={styles.timestamp}>{formatTime(message.time)}</Text>
  )}
</View>
```

## Animation Example:
```tsx
// Breathing animation
Animated.loop(
  Animated.sequence([
    Animated.timing(scaleAnim, {
      toValue: 1.02,
      duration: 2000,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: true,
    }),
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 2000,
      easing: Easing.inOut(Easing.ease),
      useNativeDriver: true,
    }),
  ])
).start();
```

## DO NOT:
- Use aggressive colors or sharp corners
- Add loading spinners or progress bars (use gentle animations)
- Include user avatars or profile pictures
- Add read receipts or "seen" indicators
- Use system fonts (must use Nunito for chat)
- Create dark mode with pure black (use soft dark blue)
- Add notification badges or unread counts
- Include emoji picker (users type emoji directly)

## MUST HAVE:
- Smooth 60fps animations
- Haptic feedback for interactions
- Keyboard avoiding view
- Pull to refresh without spinner
- Message persistence in AsyncStorage
- Emotional warmth in every detail

# Strict Scope
- Create ONLY the chat interface components listed above
- Do NOT create authentication screens
- Do NOT add navigation or tab bars
- Do NOT integrate with actual APIs (use mock data)
- Focus solely on the emotional, warm chat experience
- All text should be in Simplified Chinese where appropriate

# Mobile-First Responsive Design
- Primary: iPhone 14 Pro (390x844)
- Ensure safe areas for notch and home indicator
- Test on both iOS and Android simulators
- Landscape mode: Not supported (portrait only)
- Tablet: Scale up spacing by 1.5x, max message width 600px

Remember: This is not just a chat app. It's a safe space for emotional expression. Every pixel should convey warmth, understanding, and safety.
```

## Component-Specific Prompts

### Prompt 1: Onboarding Flow
```prompt
Create a warm, privacy-first onboarding flow for HER app. Start with anonymous mode by default - no phone number required. Use a gentle gradient background transitioning from #FAF7F2 to #FFE5D4. 

Steps:
1. Welcome screen with breathing HER logo
2. Privacy promise cards (端到端加密, 本地存储, 零评判, 随时离开)
3. Optional personalization (name, preferred chat time)
4. Biometric setup (skippable)
5. First conversation starter

Use fade and slide-up animations (300ms). Keep text minimal and warm. "很高兴认识你" not "注册成功". Skip buttons visible but de-emphasized. 
```

### Prompt 2: Message Status Indicators
```prompt
Design subtle, non-intrusive status indicators for HER chat messages:

- Sending: Message at 80% opacity with gentle pulse
- Sent: Full opacity, no indicator needed
- Failed: Inline "重试" text button, soft red (#E8A5A5)
- HER typing: Three dots with ripple animation
- HER thinking: Soft breathing glow

NO checkmarks, NO delivered/read receipts, NO timestamps unless long-pressed. Focus on emotional state not technical status.
```

### Prompt 3: Voice Input Interface
```prompt
Create a voice input overlay for HER app that feels safe and private:

1. Semi-transparent backdrop (not full black)
2. Centered recording indicator - breathing circle
3. Sound wave visualization - gentle, flowing
4. "正在倾听你的声音..." text below
5. Stop button at bottom
6. Auto-stop after 60 seconds

Colors: Use twilightPink (#F5E6E0) for waves. Animations must be smooth and calming, not sharp or robotic.
```

## Implementation Notes

### For Developers Using These Prompts:
1. **Test emotional feel**: The generated UI should make users feel safe, not judged
2. **Verify animations**: All animations should be 60fps on real devices
3. **Check accessibility**: Ensure VoiceOver/TalkBack compatibility
4. **Review Chinese text**: All user-facing text should feel warm and natural
5. **Validate privacy**: No data should leave the device without explicit consent

### Quality Checklist:
- [ ] Does it feel like a "warm digital bedroom"?
- [ ] Are interactions gentle and non-aggressive?
- [ ] Is privacy visually communicated?
- [ ] Do animations breathe rather than flash?
- [ ] Would someone feel safe sharing emotions here?

---

**Remember**: AI-generated code requires careful human review, testing, and refinement before production use. These prompts are starting points for rapid prototyping, not final implementations.