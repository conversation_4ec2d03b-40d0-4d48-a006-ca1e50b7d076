/**
 * HER Typography System
 * 温柔、易读、有人文关怀的字体系统
 */

import { Platform, PixelRatio } from 'react-native';

// 根据屏幕密度调整字体大小
const scale = (size: number): number => {
  const ratio = PixelRatio.get();
  const scaleFactor = ratio >= 3 ? 1 : ratio >= 2 ? 0.95 : 0.9;
  return Math.round(size * scaleFactor);
};

// 字体家族定义
export const fontFamilies = {
  // 中文字体
  chinese: {
    light: Platform.select({
      ios: 'PingFangSC-Light',
      android: 'NotoSansCJK-Light',
      default: 'sans-serif-light',
    }),
    regular: Platform.select({
      ios: 'PingFangSC-Regular',
      android: 'NotoSansCJK-Regular',
      default: 'sans-serif',
    }),
    medium: Platform.select({
      ios: 'PingFangSC-Medium',
      android: 'NotoSansCJK-Medium',
      default: 'sans-serif-medium',
    }),
    semibold: Platform.select({
      ios: 'PingFangSC-Semibold',
      android: 'NotoSansCJK-Bold',
      default: 'sans-serif',
    }),
  },
  // 英文字体
  english: {
    light: Platform.select({
      ios: 'Inter-Light',
      android: 'Inter-Light',
      default: 'sans-serif-light',
    }),
    regular: Platform.select({
      ios: 'Inter-Regular',
      android: 'Inter-Regular',
      default: 'sans-serif',
    }),
    medium: Platform.select({
      ios: 'Inter-Medium',
      android: 'Inter-Medium',
      default: 'sans-serif-medium',
    }),
    semibold: Platform.select({
      ios: 'Inter-SemiBold',
      android: 'Inter-SemiBold',
      default: 'sans-serif',
    }),
  },
  // 对话专用字体（更圆润）
  chat: Platform.select({
    ios: 'Nunito-Regular',
    android: 'Nunito-Regular',
    default: 'sans-serif',
  }),
};

// 字体大小定义
export const fontSizes = {
  // 标题层级
  largeTitle: scale(28),  // 仅用于欢迎页
  title1: scale(24),      // 主标题
  title2: scale(20),      // 次级标题
  title3: scale(18),      // 三级标题
  
  // 正文层级
  large: scale(17),       // 大号正文
  body: scale(16),        // 默认正文
  callout: scale(15),     // 强调文本
  subhead: scale(14),     // 副标题
  footnote: scale(13),    // 注释
  caption1: scale(12),    // 说明文字
  caption2: scale(11),    // 小说明文字
  
  // 对话专用
  chatMessage: scale(16), // 对话消息
  chatTime: scale(11),    // 时间戳
  chatHint: scale(14),    // 输入提示
  chatStatus: scale(12),  // 状态提示
};

// 行高定义
export const lineHeights = {
  tight: 1.2,    // 紧凑（标题）
  normal: 1.5,   // 正常（正文）
  relaxed: 1.8,  // 宽松（对话消息）
  loose: 2.0,    // 很宽松（诗歌、引用）
};

// 字间距定义
export const letterSpacings = {
  tighter: -0.03,  // 更紧
  tight: -0.02,    // 紧（大标题）
  normal: 0,       // 正常
  wide: 0.02,      // 宽（对话文字）
  wider: 0.04,     // 更宽
};

// 字体权重
export const fontWeights = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
};

// 预定义的文字样式
export const textStyles = {
  // 标题样式
  largeTitle: {
    fontFamily: fontFamilies.chinese.medium,
    fontSize: fontSizes.largeTitle,
    lineHeight: fontSizes.largeTitle * lineHeights.tight,
    letterSpacing: letterSpacings.tight,
    fontWeight: fontWeights.medium,
  },
  title1: {
    fontFamily: fontFamilies.chinese.medium,
    fontSize: fontSizes.title1,
    lineHeight: fontSizes.title1 * lineHeights.tight,
    letterSpacing: letterSpacings.tight,
    fontWeight: fontWeights.medium,
  },
  title2: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.title2,
    lineHeight: fontSizes.title2 * lineHeights.tight,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  title3: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.title3,
    lineHeight: fontSizes.title3 * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  
  // 正文样式
  body: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.body,
    lineHeight: fontSizes.body * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  bodyLight: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.body,
    lineHeight: fontSizes.body * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.light,
  },
  callout: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.callout,
    lineHeight: fontSizes.callout * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  subhead: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.subhead,
    lineHeight: fontSizes.subhead * lineHeights.normal,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.regular,
  },
  footnote: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.footnote,
    lineHeight: fontSizes.footnote * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  caption1: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.caption1,
    lineHeight: fontSizes.caption1 * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
  },
  caption2: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.caption2,
    lineHeight: fontSizes.caption2 * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.light,
  },
  
  // 对话专用样式
  chatMessage: {
    fontFamily: fontFamilies.chat,
    fontSize: fontSizes.chatMessage,
    lineHeight: fontSizes.chatMessage * lineHeights.relaxed,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.regular,
  },
  chatTime: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.chatTime,
    lineHeight: fontSizes.chatTime * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.light,
  },
  chatHint: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.chatHint,
    lineHeight: fontSizes.chatHint * lineHeights.normal,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.light,
    fontStyle: 'italic' as const,
  },
  chatStatus: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.chatStatus,
    lineHeight: fontSizes.chatStatus * lineHeights.normal,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.light,
  },
  
  // 特殊样式
  quote: {
    fontFamily: fontFamilies.chinese.light,
    fontSize: fontSizes.large,
    lineHeight: fontSizes.large * lineHeights.loose,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.light,
    fontStyle: 'italic' as const,
  },
  button: {
    fontFamily: fontFamilies.chinese.medium,
    fontSize: fontSizes.body,
    lineHeight: fontSizes.body * lineHeights.tight,
    letterSpacing: letterSpacings.wide,
    fontWeight: fontWeights.medium,
    textTransform: 'none' as const,
  },
  link: {
    fontFamily: fontFamilies.chinese.regular,
    fontSize: fontSizes.body,
    lineHeight: fontSizes.body * lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontWeight: fontWeights.regular,
    textDecorationLine: 'underline' as const,
  },
};

// 响应式字体缩放
export const responsiveFontSize = (
  baseSize: number,
  options?: {
    minSize?: number;
    maxSize?: number;
    scaleFactor?: number;
  }
): number => {
  const { minSize = 12, maxSize = 32, scaleFactor = 1 } = options || {};
  const scaled = scale(baseSize) * scaleFactor;
  return Math.max(minSize, Math.min(maxSize, scaled));
};

// 文字阴影效果（用于特殊场景）
export const textShadows = {
  soft: {
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  medium: {
    textShadowColor: 'rgba(0, 0, 0, 0.15)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  strong: {
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 0, height: 3 },
    textShadowRadius: 6,
  },
};

// 导出默认配置
export default {
  fontFamilies,
  fontSizes,
  lineHeights,
  letterSpacings,
  fontWeights,
  textStyles,
};