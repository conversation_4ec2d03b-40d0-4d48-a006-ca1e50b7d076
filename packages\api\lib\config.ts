/**
 * 统一配置管理器
 * 提供类型安全的环境变量访问和配置验证
 */

import { z } from 'zod';

/**
 * 环境变量验证Schema
 */
const EnvSchema = z.object({
  // Node环境
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // 应用配置
  APP_URL: z.string().url().default('https://her-app.com'),
  APP_NAME: z.string().default('HER - Private AI Companion'),
  
  // JWT配置
  JWT_SECRET: z.string().min(32, 'JWT密钥长度至少32字符'),
  
  // OpenRouter配置
  AI_PROVIDER: z.enum(['openai', 'openrouter']).default('openrouter'),
  OPENROUTER_API_KEY: z.string().min(1, 'OpenRouter API密钥不能为空'),
  OPENROUTER_MODEL: z.string().default('openai/gpt-4-turbo'),
  OPENROUTER_EMBEDDING_MODEL: z.string().default('openai/text-embedding-3-small'),
  
  // OpenAI直连配置 (可选)
  OPENAI_API_KEY: z.string().optional(),
  OPENAI_MODEL: z.string().optional(),
  
  // Supabase配置
  SUPABASE_URL: z.string().url('Supabase URL格式错误'),
  SUPABASE_SERVICE_KEY: z.string().min(1, 'Supabase服务密钥不能为空'),
  
  // Vercel KV配置
  KV_REST_API_URL: z.string().url('KV API URL格式错误'),
  KV_REST_API_TOKEN: z.string().min(1, 'KV API令牌不能为空'),
  
  // 可选配置
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  RATE_LIMIT_ENABLED: z.coerce.boolean().default(true),
  HEALTH_CHECK_ENABLED: z.coerce.boolean().default(true),
});

/**
 * 配置类型
 */
export type Config = z.infer<typeof EnvSchema>;

/**
 * 配置管理器类
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private config: Config;
  private isValidated: boolean = false;

  private constructor() {
    // 私有构造函数，实现单例模式
  }

  /**
   * 获取配置管理器实例
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * 验证和初始化配置
   */
  init(): Config {
    if (this.isValidated) {
      return this.config;
    }

    try {
      // 解析和验证环境变量
      this.config = EnvSchema.parse(process.env);
      this.isValidated = true;

      // 开发环境下输出配置信息
      if (this.config.NODE_ENV === 'development') {
        this.logConfigInfo();
      }

      return this.config;
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        const missingVars = error.errors.map(e => 
          `${e.path.join('.')}: ${e.message}`
        ).join('\n');
        
        throw new Error(`配置验证失败:\n${missingVars}`);
      }
      throw error;
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): Config {
    if (!this.isValidated) {
      return this.init();
    }
    return this.config;
  }

  /**
   * 获取特定配置项
   */
  get<K extends keyof Config>(key: K): Config[K] {
    const config = this.getConfig();
    return config[key];
  }

  /**
   * 检查配置项是否存在
   */
  has(key: keyof Config): boolean {
    const config = this.getConfig();
    return config[key] !== undefined;
  }

  /**
   * 获取数据库配置
   */
  getDatabaseConfig() {
    const config = this.getConfig();
    return {
      url: config.SUPABASE_URL,
      serviceKey: config.SUPABASE_SERVICE_KEY,
    };
  }

  /**
   * 获取AI服务配置
   */
  getAIConfig() {
    const config = this.getConfig();
    return {
      provider: config.AI_PROVIDER,
      openrouter: {
        apiKey: config.OPENROUTER_API_KEY,
        model: config.OPENROUTER_MODEL,
        embeddingModel: config.OPENROUTER_EMBEDDING_MODEL,
      },
      openai: {
        apiKey: config.OPENAI_API_KEY,
        model: config.OPENAI_MODEL,
      },
    };
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig() {
    const config = this.getConfig();
    return {
      url: config.KV_REST_API_URL,
      token: config.KV_REST_API_TOKEN,
    };
  }

  /**
   * 获取应用配置
   */
  getAppConfig() {
    const config = this.getConfig();
    return {
      name: config.APP_NAME,
      url: config.APP_URL,
      environment: config.NODE_ENV,
      logLevel: config.LOG_LEVEL,
      rateLimitEnabled: config.RATE_LIMIT_ENABLED,
      healthCheckEnabled: config.HEALTH_CHECK_ENABLED,
    };
  }

  /**
   * 获取JWT配置
   */
  getJWTConfig() {
    const config = this.getConfig();
    return {
      secret: config.JWT_SECRET,
    };
  }

  /**
   * 输出配置信息 (仅开发环境)
   */
  private logConfigInfo(): void {
    const config = this.config;
    
    console.log('🔧 Configuration loaded:');
    console.log(`   Environment: ${config.NODE_ENV}`);
    console.log(`   App URL: ${config.APP_URL}`);
    console.log(`   AI Provider: ${config.AI_PROVIDER}`);
    console.log(`   AI Model: ${config.OPENROUTER_MODEL}`);
    console.log(`   Rate Limit: ${config.RATE_LIMIT_ENABLED ? 'Enabled' : 'Disabled'}`);
    console.log(`   Log Level: ${config.LOG_LEVEL}`);
    
    // 检查可选配置
    const warnings: string[] = [];
    
    if (config.AI_PROVIDER === 'openai' && !config.OPENAI_API_KEY) {
      warnings.push('OpenAI API密钥未配置，但AI_PROVIDER设置为openai');
    }
    
    if (warnings.length > 0) {
      console.warn('⚠️  Configuration warnings:');
      warnings.forEach(warning => console.warn(`   ${warning}`));
    }
  }

  /**
   * 验证配置完整性
   */
  validate(): { valid: boolean; errors: string[] } {
    try {
      this.init();
      return { valid: true, errors: [] };
    } catch (error: any) {
      return {
        valid: false,
        errors: error.message.split('\n').filter(Boolean),
      };
    }
  }

  /**
   * 重新加载配置 (主要用于测试)
   */
  reload(): Config {
    this.isValidated = false;
    return this.init();
  }
}

/**
 * 默认配置管理器实例
 */
export const config = ConfigManager.getInstance();

/**
 * 便捷的配置获取函数
 */

/**
 * 获取完整配置
 */
export function getConfig(): Config {
  return config.getConfig();
}

/**
 * 获取特定配置项
 */
export function getConfigValue<K extends keyof Config>(key: K): Config[K] {
  return config.get(key);
}

/**
 * 获取数据库配置
 */
export function getDatabaseConfig() {
  return config.getDatabaseConfig();
}

/**
 * 获取AI服务配置
 */
export function getAIConfig() {
  return config.getAIConfig();
}

/**
 * 获取缓存配置
 */
export function getCacheConfig() {
  return config.getCacheConfig();
}

/**
 * 获取应用配置
 */
export function getAppConfig() {
  return config.getAppConfig();
}

/**
 * 获取JWT配置
 */
export function getJWTConfig() {
  return config.getJWTConfig();
}

/**
 * 初始化配置 (应用启动时调用)
 */
export function initConfig(): Config {
  return config.init();
}

/**
 * 验证配置
 */
export function validateConfig() {
  return config.validate();
}

/**
 * 是否为开发环境
 */
export function isDevelopment(): boolean {
  return getConfigValue('NODE_ENV') === 'development';
}

/**
 * 是否为生产环境
 */
export function isProduction(): boolean {
  return getConfigValue('NODE_ENV') === 'production';
}

/**
 * 是否为测试环境
 */
export function isTest(): boolean {
  return getConfigValue('NODE_ENV') === 'test';
}

/**
 * 配置预设
 */
export const CONFIG_PRESETS = {
  // 开发环境默认配置
  development: {
    LOG_LEVEL: 'debug',
    RATE_LIMIT_ENABLED: 'false',
    HEALTH_CHECK_ENABLED: 'true',
  },
  
  // 生产环境默认配置
  production: {
    LOG_LEVEL: 'warn',
    RATE_LIMIT_ENABLED: 'true',
    HEALTH_CHECK_ENABLED: 'true',
  },
  
  // 测试环境默认配置
  test: {
    LOG_LEVEL: 'error',
    RATE_LIMIT_ENABLED: 'false',
    HEALTH_CHECK_ENABLED: 'false',
  },
} as const;