/**
 * HER Mood Screen
 * 情绪记录和追踪界面
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { useTheme } from '@her/ui';

const MOOD_OPTIONS = [
  { emoji: '😊', label: '开心', value: 80 },
  { emoji: '😌', label: '平静', value: 70 },
  { emoji: '🤔', label: '思考', value: 60 },
  { emoji: '😐', label: '一般', value: 50 },
  { emoji: '😔', label: '低落', value: 30 },
  { emoji: '😢', label: '伤心', value: 20 },
  { emoji: '😡', label: '愤怒', value: 40 },
  { emoji: '😴', label: '疲惫', value: 35 },
];

export const MoodScreen: React.FC = () => {
  const theme = useTheme();
  const [selectedMood, setSelectedMood] = useState<typeof MOOD_OPTIONS[0] | null>(null);

  const handleMoodSelect = (mood: typeof MOOD_OPTIONS[0]) => {
    setSelectedMood(mood);
    // 这里可以保存心情记录
    console.log('Selected mood:', mood);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          心情记录
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
          记录此刻的心情状态
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Mood Options */}
        <View style={styles.moodGrid}>
          {MOOD_OPTIONS.map((mood, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.moodOption,
                {
                  backgroundColor: selectedMood?.label === mood.label 
                    ? theme.colors.accent 
                    : theme.colors.surface,
                  borderColor: theme.colors.border,
                },
              ]}
              onPress={() => handleMoodSelect(mood)}
            >
              <Text style={styles.moodEmoji}>{mood.emoji}</Text>
              <Text style={[styles.moodLabel, { color: theme.colors.text.primary }]}>
                {mood.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Selected Mood Display */}
        {selectedMood && (
          <View style={[styles.selectedMood, { backgroundColor: theme.colors.surface }]}>
            <Text style={[styles.selectedTitle, { color: theme.colors.text.primary }]}>
              当前心情
            </Text>
            <View style={styles.selectedContent}>
              <Text style={styles.selectedEmoji}>{selectedMood.emoji}</Text>
              <View>
                <Text style={[styles.selectedLabel, { color: theme.colors.text.primary }]}>
                  {selectedMood.label}
                </Text>
                <Text style={[styles.selectedValue, { color: theme.colors.text.secondary }]}>
                  心情值: {selectedMood.value}/100
                </Text>
              </View>
            </View>
          </View>
        )}

        {/* Mood Chart Placeholder */}
        <View style={[styles.chartContainer, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.chartTitle, { color: theme.colors.text.primary }]}>
            心情趋势
          </Text>
          <View style={styles.chartPlaceholder}>
            <Text style={[styles.placeholderText, { color: theme.colors.text.secondary }]}>
              📊 心情图表
            </Text>
            <Text style={[styles.placeholderDesc, { color: theme.colors.text.secondary }]}>
              记录更多心情数据后，这里会显示你的心情趋势图
            </Text>
          </View>
        </View>

        {/* Tips */}
        <View style={[styles.tips, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.tipsTitle, { color: theme.colors.text.primary }]}>
            💡 小贴士
          </Text>
          <Text style={[styles.tipsText, { color: theme.colors.text.secondary }]}>
            定期记录心情有助于了解自己的情绪模式，HER会根据你的心情状态提供更贴心的陪伴。
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60, // 状态栏高度
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  moodGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
    marginBottom: 32,
  },
  moodOption: {
    width: '25%',
    paddingHorizontal: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
    borderRadius: 16,
    borderWidth: 1,
    marginHorizontal: 8,
  },
  moodEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  moodLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectedMood: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  selectedTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  selectedContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedEmoji: {
    fontSize: 48,
    marginRight: 16,
  },
  selectedLabel: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedValue: {
    fontSize: 14,
  },
  chartContainer: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  chartPlaceholder: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  placeholderText: {
    fontSize: 32,
    marginBottom: 8,
  },
  placeholderDesc: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
  },
  tips: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 40,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
  },
});