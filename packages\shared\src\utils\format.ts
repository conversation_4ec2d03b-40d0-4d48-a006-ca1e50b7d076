/**
 * HER 格式化工具函数
 * 提供统一的数据格式化功能
 */

// 格式化手机号
export const formatPhoneNumber = (phone: string): string => {
  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 中国手机号格式化：138 1234 5678
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  }
  
  return phone;
};

// 掩码手机号（隐私保护）
export const maskPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  return phone;
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};

// 格式化数字（添加千位分隔符）
export const formatNumber = (num: number, locale = 'zh-CN'): string => {
  return num.toLocaleString(locale);
};

// 格式化百分比
export const formatPercentage = (value: number, decimals = 1): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};

// 格式化货币
export const formatCurrency = (amount: number, currency = 'CNY', locale = 'zh-CN'): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  }).format(amount);
};

// 格式化用户名显示
export const formatDisplayName = (user: { nickname?: string; phone?: string; id: string }): string => {
  if (user.nickname) {
    return user.nickname;
  }
  
  if (user.phone) {
    return maskPhoneNumber(user.phone);
  }
  
  // 匿名用户显示短ID
  return `用户${user.id.slice(-6).toUpperCase()}`;
};

// 格式化消息预览（截断长消息）
export const formatMessagePreview = (content: string, maxLength = 50): string => {
  if (content.length <= maxLength) {
    return content;
  }
  
  return `${content.slice(0, maxLength)}...`;
};

// 格式化标签列表
export const formatTags = (tags: string[]): string => {
  if (tags.length === 0) return '';
  
  if (tags.length === 1) return tags[0];
  
  if (tags.length === 2) return tags.join(' 和 ');
  
  return `${tags.slice(0, -1).join('、')} 和 ${tags[tags.length - 1]}`;
};

// 格式化时长（秒转为可读格式）
export const formatDurationSeconds = (seconds: number): string => {
  if (seconds < 60) {
    return `${Math.round(seconds)}秒`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
};

// 格式化URL（确保协议）
export const formatUrl = (url: string): string => {
  if (!url) return '';
  
  if (!/^https?:\/\//i.test(url)) {
    return `https://${url}`;
  }
  
  return url;
};

// 格式化JSON（美化输出）
export const formatJson = (obj: any, indent = 2): string => {
  try {
    return JSON.stringify(obj, null, indent);
  } catch {
    return String(obj);
  }
};

// 格式化错误消息
export const formatErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.error?.message) {
    return error.error.message;
  }
  
  return '发生了未知错误';
};

// 首字母大写
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// 驼峰转换为可读文本
export const camelToReadable = (camelCase: string): string => {
  return camelCase
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};