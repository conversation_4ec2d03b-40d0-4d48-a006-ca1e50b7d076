{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "strict": true, "jsx": "react-jsx", "allowJs": true, "moduleResolution": "node", "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"], "@/hooks/*": ["./src/hooks/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@her/*": ["../../packages/shared/src/*"]}}, "include": ["src/**/*", "App.tsx", "app.config.ts"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"], "references": [{"path": "../../packages/shared"}, {"path": "../../packages/ui"}]}