module.exports = {
  projects: [
    // 共享包测试配置
    {
      displayName: 'shared',
      testMatch: ['<rootDir>/packages/shared/**/*.{test,spec}.{js,ts}'],
      testEnvironment: 'node',
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: 'packages/shared/tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@her/(.*)$': '<rootDir>/packages/shared/src/$1',
      },
    },
    
    // UI包测试配置
    {
      displayName: 'ui',
      testMatch: ['<rootDir>/packages/ui/**/*.{test,spec}.{js,ts,tsx}'],
      testEnvironment: 'jsdom',
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: 'packages/ui/tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@her/(.*)$': '<rootDir>/packages/shared/src/$1',
      },
      setupFilesAfterEnv: ['<rootDir>/packages/ui/jest.setup.js'],
    },
    
    // API包测试配置
    {
      displayName: 'api',
      testMatch: ['<rootDir>/packages/api/**/*.{test,spec}.{js,ts}'],
      testEnvironment: 'node',
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: 'packages/api/tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@her/(.*)$': '<rootDir>/packages/shared/src/$1',
      },
    },
    
    // 移动应用测试配置
    {
      displayName: 'mobile',
      testMatch: ['<rootDir>/apps/mobile/**/*.{test,spec}.{js,ts,tsx}'],
      preset: 'jest-expo',
      transform: {
        '^.+\\.(ts|tsx)$': ['ts-jest', {
          tsconfig: 'apps/mobile/tsconfig.json',
        }],
      },
      moduleNameMapping: {
        '^@/(.*)$': '<rootDir>/apps/mobile/src/$1',
        '^@her/(.*)$': '<rootDir>/packages/shared/src/$1',
      },
      transformIgnorePatterns: [
        'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)',
      ],
    },
  ],
  
  // 全局配置
  collectCoverageFrom: [
    'packages/**/*.{ts,tsx}',
    'apps/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/dist/**',
    '!**/coverage/**',
    '!**/*.config.*',
  ],
  
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 设置覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};