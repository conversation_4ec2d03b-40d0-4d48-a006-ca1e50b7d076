/**
 * ChatBubble Component
 * HER的核心对话气泡组件 - 传递温暖和理解
 * 从 src/components/core/ChatBubble.tsx 迁移而来
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  TouchableOpacity,
  Vibration,
} from 'react-native';
import { designTokens, EmotionType } from '@her/shared';
import { useTheme } from '../theme/ThemeProvider';

const { width: screenWidth } = Dimensions.get('window');

export interface ChatBubbleProps {
  // 核心属性
  type: 'her' | 'user';
  message: string;
  timestamp?: string;
  
  // 情感状态
  emotion?: EmotionType;
  
  // 状态指示
  isTyping?: boolean;
  isFirst?: boolean;  // 是否是一组消息中的第一条
  isLast?: boolean;   // 是否是一组消息中的最后一条
  
  // 交互
  onPress?: () => void;
  onLongPress?: () => void;
}

export const ChatBubble: React.FC<ChatBubbleProps> = ({
  type,
  message,
  timestamp,
  emotion = 'neutral',
  isTyping = false,
  isFirst = true,
  isLast = true,
  onPress,
  onLongPress,
}) => {
  const theme = useTheme();
  
  // 动画值
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(10)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const typingAnim = useRef(new Animated.Value(0)).current;

  // 入场动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: designTokens.animation.duration.normal,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: designTokens.animation.duration.normal,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // 输入中动画（呼吸效果）
  useEffect(() => {
    if (isTyping) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(typingAnim, {
            toValue: 1,
            duration: designTokens.animation.duration.breathing / 2,
            useNativeDriver: true,
          }),
          Animated.timing(typingAnim, {
            toValue: 0,
            duration: designTokens.animation.duration.breathing / 2,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [isTyping]);

  // 长按触感反馈
  const handleLongPress = () => {
    Vibration.vibrate(10); // 轻微震动
    onLongPress?.();
  };

  // 获取气泡颜色
  const getBubbleColor = () => {
    if (type === 'her') {
      switch (emotion) {
        case 'warm':
        case 'caring': 
          return designTokens.emotionalColors.caring.bubble;
        case 'listening': 
          return designTokens.emotionalColors.listening.bubble;
        case 'thinking': 
          return designTokens.emotionalColors.thinking.bubble;
        case 'understanding':
          return designTokens.emotionalColors.understanding.bubble;
        default: 
          return theme.colors.bubble.her;
      }
    }
    return theme.colors.bubble.user;
  };

  // 渲染输入中指示器
  const renderTypingIndicator = () => (
    <Animated.View 
      style={[
        styles.typingIndicator,
        {
          opacity: typingAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 1],
          }),
          transform: [{
            scale: typingAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.95, 1.05],
            }),
          }],
        },
      ]}
    >
      <View style={[styles.typingDot, { backgroundColor: theme.colors.text.secondary }]} />
      <View style={[styles.typingDot, styles.typingDotMiddle, { backgroundColor: theme.colors.text.secondary }]} />
      <View style={[styles.typingDot, { backgroundColor: theme.colors.text.secondary }]} />
    </Animated.View>
  );

  // 渲染消息内容
  const renderContent = () => {
    if (isTyping) {
      return renderTypingIndicator();
    }

    return (
      <Text style={[
        styles.messageText,
        { 
          color: theme.colors.text.primary,
          fontFamily: designTokens.typography.fontFamilies.chat,
          fontSize: designTokens.typography.fontSizes.body,
          lineHeight: designTokens.typography.fontSizes.body * designTokens.typography.lineHeights.normal,
        },
        type === 'user' && styles.userMessageText,
      ]}>
        {message}
      </Text>
    );
  };

  return (
    <Animated.View
      style={[
        styles.container,
        type === 'user' ? styles.userContainer : styles.herContainer,
        {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim },
          ],
        },
      ]}
    >
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        onLongPress={handleLongPress}
        delayLongPress={500}
      >
        <View
          style={[
            styles.bubble,
            type === 'user' ? styles.userBubble : styles.herBubble,
            {
              backgroundColor: getBubbleColor(),
              borderTopLeftRadius: type === 'her' && !isFirst ? 8 : designTokens.borderRadius.bubble,
              borderTopRightRadius: type === 'user' && !isFirst ? 8 : designTokens.borderRadius.bubble,
              borderBottomLeftRadius: type === 'her' && !isLast ? 8 : designTokens.borderRadius.bubble,
              borderBottomRightRadius: type === 'user' && !isLast ? 8 : designTokens.borderRadius.bubble,
              ...getShadowStyle(designTokens.shadows.bubble),
            },
            isTyping && styles.typingBubble,
          ]}
        >
          {renderContent()}
        </View>
      </TouchableOpacity>
      
      {timestamp && isLast && (
        <Text style={[
          styles.timestamp,
          {
            color: theme.colors.text.secondary,
            fontSize: designTokens.typography.fontSizes.caption1,
          },
          type === 'user' && styles.userTimestamp,
        ]}>
          {timestamp}
        </Text>
      )}
    </Animated.View>
  );
};

// 辅助函数：转换阴影样式
const getShadowStyle = (shadow: typeof designTokens.shadows.bubble) => ({
  shadowColor: shadow.color,
  shadowOffset: {
    width: shadow.offsetX,
    height: shadow.offsetY,
  },
  shadowOpacity: 1,
  shadowRadius: shadow.blur,
  elevation: shadow.offsetY + shadow.blur, // Android 阴影
});

const styles = StyleSheet.create({
  container: {
    marginVertical: designTokens.spacing.chat.betweenMessages / 2,
    paddingHorizontal: designTokens.spacing.chat.bubblePaddingH,
  },
  
  herContainer: {
    alignItems: 'flex-start',
  },
  
  userContainer: {
    alignItems: 'flex-end',
  },
  
  bubble: {
    maxWidth: screenWidth * designTokens.layout.maxBubbleWidthRatio,
    paddingHorizontal: designTokens.spacing.chat.bubblePaddingH,
    paddingVertical: designTokens.spacing.chat.bubblePaddingV,
  },
  
  herBubble: {
    marginRight: screenWidth * (1 - designTokens.layout.maxBubbleWidthRatio),
  },
  
  userBubble: {
    marginLeft: screenWidth * (1 - designTokens.layout.maxBubbleWidthRatio),
  },
  
  typingBubble: {
    paddingVertical: designTokens.spacing.chat.bubblePaddingV + 4,
  },
  
  messageText: {
    // 动态样式在组件中设置
  },
  
  userMessageText: {
    // 用户消息特殊样式（如果需要）
  },
  
  timestamp: {
    marginTop: designTokens.spacing.xxs,
    marginHorizontal: designTokens.spacing.xxs,
  },
  
  userTimestamp: {
    textAlign: 'right',
  },
  
  // 输入中指示器样式
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20,
  },
  
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    opacity: 0.6,
  },
  
  typingDotMiddle: {
    marginHorizontal: 4,
  },
});