/**
 * /api/chat/conversations - 获取用户对话列表
 * 使用方法：GET /api/chat/conversations 获取用户的对话历史
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { createClient } from '@supabase/supabase-js';

import { withAuth, AuthenticatedRequest } from '../../middleware/auth';
import { withHerCors } from '../../middleware/cors';
import { withErrorHandler, APIErrors, combineMiddlewares } from '../../middleware/error-handler';
import { withRateLimit, RATE_LIMIT_CONFIGS } from '../../lib/rate-limiter';
import { GetConversationsRequestSchema } from '../../types/chat';
import type { GetConversationsRequest, GetConversationsResponse } from '../../types/chat';

/**
 * 获取对话列表处理函数
 */
async function getConversationsHandler(req: AuthenticatedRequest, res: VercelResponse): Promise<void> {
  // 只允许GET请求
  if (req.method !== 'GET') {
    throw APIErrors.BAD_REQUEST(`不支持的HTTP方法: ${req.method}`);
  }

  try {
    // 验证查询参数
    const query = GetConversationsRequestSchema.parse(req.query);
    const { page, limit, archived } = query;

    // 计算偏移量
    const offset = (page - 1) * limit;

    // 初始化Supabase客户端
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_KEY!
    );

    console.log('Fetching conversations:', {
      userId: req.userId,
      page,
      limit,
      offset,
      archived,
    });

    // 构建查询
    let query_builder = supabase
      .from('conversations')
      .select(`
        id,
        title,
        created_at,
        updated_at,
        metadata,
        is_archived
      `)
      .eq('user_id', req.userId)
      .order('updated_at', { ascending: false });

    // 添加归档过滤
    if (archived !== undefined) {
      query_builder = query_builder.eq('is_archived', archived);
    } else {
      // 默认不包含已归档的对话
      query_builder = query_builder.eq('is_archived', false);
    }

    // 执行分页查询
    const { data: conversations, error: fetchError } = await query_builder
      .range(offset, offset + limit - 1);

    if (fetchError) {
      console.error('Database query error:', fetchError);
      throw APIErrors.DATABASE_ERROR('获取对话列表失败');
    }

    // 获取总数
    const { count, error: countError } = await supabase
      .from('conversations')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', req.userId)
      .eq('is_archived', archived ?? false);

    if (countError) {
      console.error('Count query error:', countError);
      throw APIErrors.DATABASE_ERROR('获取对话总数失败');
    }

    const total = count || 0;
    const totalPages = Math.ceil(total / limit);

    // 格式化响应数据
    const formattedConversations = (conversations || []).map(conv => ({
      id: conv.id,
      userId: req.userId,
      title: conv.title,
      createdAt: conv.created_at,
      updatedAt: conv.updated_at,
      lastMessageAt: conv.metadata?.lastMessageAt || conv.updated_at,
      messageCount: conv.metadata?.messageCount || 0,
      metadata: {
        totalTokens: conv.metadata?.totalTokens,
        avgEmotionScore: conv.metadata?.avgEmotionScore,
        lastEmotion: conv.metadata?.lastEmotion,
      },
      isArchived: conv.is_archived,
    }));

    const response: GetConversationsResponse = {
      success: true,
      data: {
        conversations: formattedConversations,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    };

    console.log('Conversations fetched:', {
      userId: req.userId,
      count: formattedConversations.length,
      total,
      page,
    });

    res.status(200).json(response);

  } catch (error: any) {
    console.error('Get conversations error:', error);
    
    if (error.name === 'ZodError') {
      throw APIErrors.VALIDATION_ERROR('查询参数验证失败', error.errors);
    }

    throw error;
  }
}

/**
 * 导出带中间件的处理函数
 */
export default combineMiddlewares(
  getConversationsHandler,
  [
    withErrorHandler,
    withHerCors,
    withAuth,
    (handler) => withRateLimit(handler, RATE_LIMIT_CONFIGS.GENERAL),
  ]
);

// 导出处理函数供测试使用
export { getConversationsHandler };