/**
 * HER 设计令牌系统
 * 基于现有的 design-tokens.json 转换为 TypeScript
 */

import { 
  ColorPalette, 
  Typography, 
  Spacing, 
  BorderRadius, 
  Shadows, 
  Animation,
  ThemeConfig,
  EmotionalColors
} from '../types/theme';

// 基础色彩定义
export const baseColors = {
  // 主色调
  morningCream: '#FAF7F2',    // 晨曦米白
  twilightPink: '#F5E6E0',     // 暮光粉
  moonlightBlue: '#E8EEF5',    // 月光蓝
  candleOrange: '#FFE5D4',     // 烛光橙
  
  // 深色调
  midnightBlue: '#2C3E50',     // 午夜蓝
  deepNavy: '#34495E',         // 深海军蓝
  
  // 语义色彩
  success: '#95C99F',          // 柔和绿
  warning: '#F4C67F',          // 温柔黄
  error: '#E8A5A5',            // 柔和红
  info: '#A5C4E8',             // 淡蓝
  
  // 中性色
  white: '#FFFFFF',
  black: '#000000',
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
  },
} as const;

// 情绪化颜色
export const emotionalColors: EmotionalColors = {
  listening: {
    primary: '#E8F4F8',
    accent: '#7FC4D6',
    bubble: '#D6E9F0',
  },
  thinking: {
    primary: '#F8F4E8',
    accent: '#D6C47F',
    bubble: '#F0E9D6',
  },
  caring: {
    primary: '#F8E8F4',
    accent: '#D67FA6',
    bubble: '#F0D6E9',
  },
  understanding: {
    primary: '#E8F8E8',
    accent: '#7FD67F',
    bubble: '#D6F0D6',
  },
} as const;

// 字体系统
export const typography: Typography = {
  fontFamilies: {
    chinese: 'PingFangSC, NotoSansCJK, sans-serif',
    english: 'Inter, -apple-system, sans-serif',
    chat: 'Nunito, PingFangSC, sans-serif',
  },
  fontSizes: {
    largeTitle: 28,
    title1: 24,
    title2: 20,
    title3: 18,
    large: 17,
    body: 16,
    callout: 15,
    subhead: 14,
    footnote: 13,
    caption1: 12,
    caption2: 11,
  },
  lineHeights: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.8,
    loose: 2.0,
  },
  fontWeights: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },
} as const;

// 间距系统
export const spacing: Spacing = {
  scale: {
    xxs: 4,
    xs: 8,
    sm: 12,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
    xxxl: 64,
  },
  chat: {
    betweenMessages: 12,
    bubblePaddingH: 16,
    bubblePaddingV: 12,
    inputPadding: 16,
  },
} as const;

// 圆角系统
export const borderRadius: BorderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  pill: 999,
  bubble: 16,
  button: 12,
  card: 16,
  input: 24,
} as const;

// 阴影系统
export const shadows: Shadows = {
  xs: {
    color: 'rgba(0, 0, 0, 0.05)',
    offsetX: 0,
    offsetY: 1,
    blur: 2,
    spread: 0,
  },
  sm: {
    color: 'rgba(0, 0, 0, 0.08)',
    offsetX: 0,
    offsetY: 2,
    blur: 4,
    spread: 0,
  },
  md: {
    color: 'rgba(0, 0, 0, 0.10)',
    offsetX: 0,
    offsetY: 4,
    blur: 8,
    spread: 0,
  },
  lg: {
    color: 'rgba(0, 0, 0, 0.12)',
    offsetX: 0,
    offsetY: 8,
    blur: 16,
    spread: 0,
  },
  bubble: {
    color: 'rgba(0, 0, 0, 0.08)',
    offsetX: 0,
    offsetY: 2,
    blur: 8,
    spread: 0,
  },
} as const;

// 动画系统
export const animation: Animation = {
  duration: {
    instant: 100,
    fast: 200,
    normal: 300,
    slow: 500,
    verySlow: 1000,
    breathing: 2000,
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    spring: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  },
} as const;

// 主题定义
export const themes: Record<string, ThemeConfig> = {
  default: {
    name: '默认主题',
    mode: 'light',
    colors: {
      primary: baseColors.morningCream,
      secondary: baseColors.twilightPink,
      background: baseColors.morningCream,
      surface: baseColors.white,
      text: {
        primary: baseColors.gray[800],
        secondary: baseColors.gray[600],
        disabled: baseColors.gray[400],
      },
      bubble: {
        her: baseColors.twilightPink,
        user: baseColors.moonlightBlue,
      },
      accent: baseColors.candleOrange,
      border: `${baseColors.gray[200]}40`,
      shadow: 'rgba(0, 0, 0, 0.08)',
    },
  },
  peaceful: {
    name: '宁静模式',
    mode: 'light',
    colors: {
      background: '#F0F4F8',
      surface: baseColors.white,
      bubble: {
        her: '#E0E8F5',
        user: '#D4E2F5',
      },
      accent: baseColors.info,
    } as Partial<ColorPalette>,
  },
  warm: {
    name: '温暖模式',
    mode: 'light',
    colors: {
      background: '#FFF9F5',
      surface: baseColors.white,
      bubble: {
        her: baseColors.candleOrange,
        user: baseColors.twilightPink,
      },
      accent: '#FFA07A',
    } as Partial<ColorPalette>,
  },
  night: {
    name: '深夜模式',
    mode: 'dark',
    colors: {
      primary: baseColors.midnightBlue,
      secondary: baseColors.deepNavy,
      background: baseColors.gray[900],
      surface: baseColors.gray[800],
      text: {
        primary: baseColors.gray[100],
        secondary: baseColors.gray[300],
        disabled: baseColors.gray[500],
      },
      bubble: {
        her: baseColors.deepNavy,
        user: '#3A506B',
      },
      accent: '#5A7A9A',
      border: `${baseColors.gray[700]}60`,
      shadow: 'rgba(0, 0, 0, 0.3)',
    },
  },
} as const;

// 布局常量
export const layout = {
  maxContentWidth: 600,
  maxBubbleWidthRatio: 0.75,
  minTouchTarget: 44,
  headerHeight: 56,
  tabBarHeight: 49,
} as const;

// 图标尺寸
export const iconSizes = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 28,
  xl: 32,
} as const;

// 头像尺寸
export const avatarSizes = {
  xs: 24,
  sm: 32,
  md: 40,
  lg: 48,
  xl: 56,
} as const;

// 平台特定配置
export const platformConfig = {
  ios: {
    statusBarHeight: 44,
    homeIndicatorHeight: 34,
    fontFamily: 'PingFangSC',
  },
  android: {
    statusBarHeight: 24,
    navigationBarHeight: 48,
    fontFamily: 'NotoSansCJK',
  },
} as const;

// 渐变色定义
export const gradients = {
  warmSunrise: [baseColors.candleOrange, baseColors.twilightPink],
  coolEvening: [baseColors.moonlightBlue, '#E0E8F5'],
  nightfall: [baseColors.deepNavy, baseColors.midnightBlue],
  peaceful: ['#D4E2F5', '#E0E8F5'],
  gentle: [baseColors.twilightPink, baseColors.morningCream],
} as const;

// 默认设计令牌导出
const designTokens = {
  colors: baseColors,
  emotionalColors,
  typography,
  spacing,
  borderRadius,
  shadows,
  animation,
  themes,
  layout,
  iconSizes,
  avatarSizes,
  platformConfig,
  gradients,
} as const;

export default designTokens;