/**
 * Health Check Endpoint
 * 系统健康检查接口 - 支持AI聊天API状态检查
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { withHerCors } from '../middleware/cors';
import { withErrorHandler, combineMiddlewares } from '../middleware/error-handler';
import { createAIClientFromEnv } from '../src/lib/ai-provider';

async function healthHandler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: '不支持的HTTP方法',
      }
    });
  }
  
  try {
    const startTime = Date.now();
    
    // 基础健康信息
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      services: {
        ai: 'unknown',
        database: 'unknown',
        cache: 'unknown',
      },
      responseTime: 0,
    };

    // 检查AI服务状态
    try {
      const aiClient = createAIClientFromEnv();
      const isConnected = await aiClient.testConnection();
      healthData.services.ai = isConnected ? 'healthy' : 'unhealthy';
    } catch (error) {
      healthData.services.ai = 'unhealthy';
      console.warn('AI service health check failed:', error);
    }

    // TODO: 添加数据库和缓存健康检查
    healthData.services.database = 'healthy'; // 暂时默认健康
    healthData.services.cache = 'healthy';     // 暂时默认健康

    healthData.responseTime = Date.now() - startTime;

    // 确定总体状态
    const allServicesHealthy = Object.values(healthData.services).every(
      status => status === 'healthy'
    );
    
    if (!allServicesHealthy) {
      healthData.status = 'degraded';
    }
    
    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    
    return res.status(statusCode).json({
      success: healthData.status === 'healthy',
      data: healthData,
    });
    
  } catch (error) {
    console.error('Health check error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: '健康检查失败',
        timestamp: new Date().toISOString(),
      }
    });
  }
}

export default combineMiddlewares(
  healthHandler,
  [
    withErrorHandler,
    withHerCors,
  ]
);