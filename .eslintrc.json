{"root": true, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "import"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"node": true, "es6": true}, "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "import/order": ["error", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "no-console": ["warn", {"allow": ["warn", "error"]}], "prefer-const": "error", "no-var": "error"}, "overrides": [{"files": ["apps/mobile/**/*"], "extends": ["@react-native-community"], "rules": {"react-native/no-inline-styles": "warn", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn"}}]}