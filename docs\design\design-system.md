# HER Design System (设计系统)

> "在这个不敢倾诉的时代，HER是你最私密、最安全的心灵避风港"

## 设计理念 🎨

### 核心概念：温暖的数字卧室

HER的设计理念围绕着创造一个"温暖的数字卧室" - 一个让用户感到绝对安全、可以完全放松的私密空间。每一个设计决策都服务于这个核心理念。

### 设计原则

1. **极简但不冷漠** - 界面简洁，但充满温暖的细节
2. **存在但不打扰** - 恰到好处的陪伴感，不会让用户感到压迫
3. **私密如日记本** - 视觉上传达绝对的安全感和私密性
4. **情感优于功能** - 每个交互都传递温度，而非冰冷的效率

## 视觉语言系统 🎨

### 色彩系统

#### 主色调
```
晨曦米白 (Morning Cream)    #FAF7F2    主背景色，温暖柔和
暮光粉 (Twilight Pink)      #F5E6E0    HER消息气泡
月光蓝 (Moonlight Blue)     #E8EEF5    用户消息气泡
烛光橙 (Candle Orange)      #FFE5D4    强调色，温暖提示
```

#### 情绪适应色彩
```
宁静模式 (Peaceful)
  - 主色：#E8EEF5 → #E0E8F5
  - 渐变：淡蓝紫色调
  - 使用场景：深夜倾听、需要平静时

温暖模式 (Warm)
  - 主色：#FFE5D4 → #F5E6E0
  - 渐变：粉橙色调
  - 使用场景：日常陪伴、温馨时刻

深夜模式 (Night)
  - 主色：#2C3E50 → #34495E
  - 渐变：深蓝墨色
  - 使用场景：夜间使用、保护眼睛
```

#### 语义色彩
```
成功/积极  #95C99F (柔和绿)    不要过于鲜艳
警告/提醒  #F4C67F (温柔黄)    避免焦虑感
错误/危险  #E8A5A5 (柔和红)    温和提示
信息/中性  #A5C4E8 (淡蓝)      轻柔传达
```

### 字体系统

#### 字体选择
- **中文主字体**: 思源黑体 (Source Han Sans)
  - 特点：圆润、友好、易读
  - 权重：Light (300), Regular (400), Medium (500)
  
- **英文主字体**: Inter
  - 特点：现代、清晰、人文感
  - 权重：Light (300), Regular (400), Medium (500)

- **对话专用字体**: 更加柔和的圆体
  - 中文：思源圆体
  - 英文：Nunito

#### 字体大小规范
```typescript
const typography = {
  largeTitle: 28,    // 仅用于欢迎页
  title1: 24,        // 主标题
  title2: 20,        // 次级标题
  body: 16,          // 正文（默认）
  callout: 15,       // 强调文本
  subhead: 14,       // 副标题
  footnote: 13,      // 注释
  caption: 12,       // 说明文字
  
  // 对话专用
  chatMessage: 16,   // 对话消息
  chatTime: 11,      // 时间戳
  chatHint: 14,      // 输入提示
};
```

#### 行高与间距
```typescript
const lineHeight = {
  tight: 1.2,     // 标题
  normal: 1.5,    // 正文
  relaxed: 1.8,   // 对话消息（更舒适的阅读体验）
};

const letterSpacing = {
  tight: -0.02,   // 大标题
  normal: 0,      // 正文
  relaxed: 0.02,  // 对话文字（略微增加间距）
};
```

### 图标系统

#### 图标风格
- **线条风格**: 2px圆角线条，避免尖锐边角
- **大小规格**: 20x20, 24x24, 28x28
- **动效**: 轻柔的呼吸动画或缩放效果

#### 核心图标集
```
导航类：
  首页: 圆润的房子轮廓
  对话: 两个重叠的聊天气泡
  回忆: 柔和的时钟
  设置: 圆形齿轮

功能类：
  发送: 纸飞机（圆润造型）
  语音: 声波纹
  表情: 微笑脸
  更多: 三个圆点

状态类：
  倾听中: 涟漪动画
  思考中: 柔和光点
  在线: 呼吸的光晕
  隐私: 温柔的锁
```

### 间距系统

```typescript
const spacing = {
  xxs: 4,
  xs: 8,
  sm: 12,
  md: 16,  // 基础间距
  lg: 24,
  xl: 32,
  xxl: 48,
};

// 特殊间距
const chatSpacing = {
  betweenMessages: 12,      // 消息间距
  bubblePadding: 16,        // 气泡内边距
  senderMargin: 8,          // 发送者名称间距
  timeStampMargin: 4,       // 时间戳间距
};
```

## 组件设计规范 📦

### 对话气泡 (ChatBubble)

```typescript
interface ChatBubbleProps {
  type: 'her' | 'user';
  message: string;
  timestamp?: string;
  emotion?: 'neutral' | 'warm' | 'caring' | 'listening';
  isTyping?: boolean;
}
```

**设计要点：**
- 圆角：16px (柔和感)
- 阴影：极轻微 (0 2px 8px rgba(0,0,0,0.08))
- 最大宽度：屏幕宽度的 75%
- 动画：淡入 + 轻微上移 (300ms ease-out)

### 输入框 (MessageInput)

```typescript
interface MessageInputProps {
  placeholder?: string;
  maxHeight?: number;
  showVoiceButton?: boolean;
  mood?: 'default' | 'night' | 'peaceful';
}
```

**设计要点：**
- 圆角：24px (药丸形状)
- 背景：半透明白色
- 自动增高：最多4行
- 焦点效果：温柔的光晕

### 情感加载器 (EmotionalLoader)

```typescript
interface EmotionalLoaderProps {
  type: 'listening' | 'thinking' | 'understanding';
  size?: 'small' | 'medium' | 'large';
  color?: string;
}
```

**动画设计：**
- 倾听中：三个点的涟漪扩散
- 思考中：柔和的呼吸光晕
- 理解中：温暖的脉冲动画

## 交互设计 ⚡

### 触摸反馈

```typescript
const hapticFeedback = {
  light: {
    ios: 'impactLight',
    android: 'effectTick',
    duration: 10
  },
  medium: {
    ios: 'impactMedium',
    android: 'effectClick',
    duration: 20
  }
};
```

### 动效时间曲线

```typescript
const animations = {
  // 进入动画
  fadeIn: {
    duration: 300,
    easing: 'ease-out',
    from: { opacity: 0, translateY: 10 },
    to: { opacity: 1, translateY: 0 }
  },
  
  // 呼吸动画
  breathing: {
    duration: 2000,
    easing: 'ease-in-out',
    loop: true,
    from: { scale: 1 },
    to: { scale: 1.05 }
  },
  
  // 触摸反馈
  touch: {
    duration: 100,
    easing: 'ease-out',
    scale: 0.98
  }
};
```

### 手势交互

```typescript
const gestures = {
  // 下拉刷新
  pullToRefresh: {
    threshold: 80,
    resistance: 2.5,
    snapBack: 300
  },
  
  // 滑动返回
  swipeBack: {
    threshold: 50,
    velocity: 0.3,
    animation: 'parallax'
  },
  
  // 长按菜单
  longPress: {
    minDuration: 500,
    movement: 10,
    feedback: 'haptic-light'
  }
};
```

## 场景化设计 🌙

### 时间感知

```typescript
const timeBasedThemes = {
  morning: {    // 6:00 - 12:00
    greeting: "早安，新的一天",
    colors: { primary: '#FAF7F2', accent: '#FFE5D4' },
    mood: 'energetic'
  },
  afternoon: {  // 12:00 - 18:00
    greeting: "午后时光",
    colors: { primary: '#F5E6E0', accent: '#E8EEF5' },
    mood: 'calm'
  },
  evening: {    // 18:00 - 22:00
    greeting: "晚上好",
    colors: { primary: '#E8EEF5', accent: '#F5E6E0' },
    mood: 'warm'
  },
  night: {      // 22:00 - 6:00
    greeting: "深夜了，还好吗",
    colors: { primary: '#34495E', accent: '#2C3E50' },
    mood: 'peaceful'
  }
};
```

### 情绪响应

```typescript
const emotionalResponses = {
  tired: {
    bubbleColor: 'softer',
    animationSpeed: 'slower',
    messageDelay: 'longer',
    tone: 'gentle'
  },
  anxious: {
    bubbleColor: 'calming',
    animationSpeed: 'smooth',
    messageDelay: 'immediate',
    tone: 'reassuring'
  },
  happy: {
    bubbleColor: 'warmer',
    animationSpeed: 'lively',
    messageDelay: 'normal',
    tone: 'cheerful'
  }
};
```

## 无障碍设计 ♿

### 可访问性要求

1. **颜色对比度**
   - 正文文字: 最小 4.5:1
   - 大文字: 最小 3:1
   - 提供高对比度模式

2. **触摸目标**
   - 最小尺寸: 44x44pt
   - 间距: 至少 8pt

3. **屏幕阅读器**
   ```typescript
   accessibilityLabel="发送消息按钮"
   accessibilityHint="双击发送您的消息"
   accessibilityRole="button"
   ```

4. **动效控制**
   - 提供关闭动画选项
   - 遵循系统的减少动效设置

### 文字缩放支持

```typescript
const scalableText = {
  minScaleFactor: 0.85,
  maxScaleFactor: 1.5,
  adjustsFontSizeToFit: true,
  allowFontScaling: true
};
```

## 性能优化指南 🚀

### 图片优化
- 使用 WebP 格式
- 提供 @1x, @2x, @3x 版本
- 懒加载非关键图片
- 图片最大尺寸：头像 200x200, 背景 1920x1080

### 动画性能
- 使用 `useNativeDriver: true`
- 避免动画影响布局
- 限制同时运行的动画数量
- 使用 InteractionManager 延迟非关键动画

### 内存管理
- 限制对话历史缓存：最近 100 条
- 图片缓存上限：50MB
- 定期清理未使用资源
- 使用 FlatList 而非 ScrollView

## 实施指南 📝

### 文件结构
```
src/
  theme/
    colors.ts        # 颜色定义
    typography.ts    # 字体系统
    spacing.ts       # 间距系统
    animations.ts    # 动画配置
  components/
    core/           # 核心组件
      ChatBubble.tsx
      MessageInput.tsx
      EmotionalLoader.tsx
    screens/        # 页面组件
    shared/         # 共享组件
  utils/
    responsive.ts   # 响应式工具
    haptic.ts      # 触感反馈
```

### 使用示例

```tsx
// 导入主题
import { colors, typography, spacing } from '@/theme';

// 使用设计系统
const ChatScreen = () => {
  return (
    <View style={{
      backgroundColor: colors.background.primary,
      padding: spacing.md
    }}>
      <Text style={{
        ...typography.body,
        color: colors.text.primary
      }}>
        HER is listening...
      </Text>
    </View>
  );
};
```

## 版本管理

| 版本 | 日期 | 更新内容 |
|-----|------|---------|
| 1.0.0 | 2025-01-09 | 初始设计系统 |

---

_"设计不仅是外观和感觉，设计是它的工作方式。" - Steve Jobs_

_HER Design System - 为温暖的陪伴而设计_ 💝