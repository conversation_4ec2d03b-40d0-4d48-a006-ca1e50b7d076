/**
 * Stream Handler - SSE流式响应处理器
 * 处理OpenRouter API的流式响应，转换为Server-Sent Events格式
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import type OpenAI from 'openai';
import { randomUUID } from 'crypto';
import type { StreamChunk, SSEEvent } from '../types/chat';

export interface StreamContext {
  messageId: string;
  conversationId: string;
  userId: string;
  model: string;
  startTime: number;
}

/**
 * 流式响应处理器类
 */
export class StreamHandler {
  private res: VercelResponse;
  private context: StreamContext;
  private buffer: string = '';
  private tokenCount: number = 0;
  private isEnded: boolean = false;

  constructor(res: VercelResponse, context: StreamContext) {
    this.res = res;
    this.context = context;
    this.setupSSEHeaders();
  }

  /**
   * 设置SSE响应头
   */
  private setupSSEHeaders(): void {
    this.res.setHeader('Content-Type', 'text/event-stream');
    this.res.setHeader('Cache-Control', 'no-cache');
    this.res.setHeader('Connection', 'keep-alive');
    this.res.setHeader('Access-Control-Allow-Origin', '*');
    this.res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    // 发送初始ping以建立连接
    this.sendPing();
  }

  /**
   * 处理OpenAI流式响应
   */
  async processStream(stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>): Promise<void> {
    try {
      // 发送开始事件
      this.sendEvent('message-start', {
        messageId: this.context.messageId,
        conversationId: this.context.conversationId,
        model: this.context.model,
        timestamp: new Date().toISOString(),
      });

      let fullContent = '';

      // 处理流式数据块
      for await (const chunk of stream) {
        if (this.isEnded) break;

        const delta = chunk.choices?.[0]?.delta;
        if (!delta) continue;

        // 处理内容增量
        if (delta.content) {
          const content = delta.content;
          this.buffer += content;
          fullContent += content;
          this.tokenCount++;

          // 发送内容增量
          this.sendContentDelta(content);

          // 定期刷新缓冲区（避免单次发送过多数据）
          if (this.buffer.length > 100) {
            await this.flushBuffer();
          }
        }

        // 处理函数调用（如果支持）
        if (delta.function_call) {
          this.sendEvent('function-call', {
            name: delta.function_call.name,
            arguments: delta.function_call.arguments,
          });
        }

        // 处理工具调用（GPT-4新格式）
        if (delta.tool_calls) {
          this.sendEvent('tool-calls', {
            tool_calls: delta.tool_calls,
          });
        }
      }

      // 刷新最后的缓冲区
      await this.flushBuffer();

      // 发送完成事件
      await this.sendCompletionEvent(fullContent);

    } catch (error: any) {
      console.error('Stream processing error:', error);
      this.sendError(error.message || 'Stream processing failed');
    } finally {
      this.end();
    }
  }

  /**
   * 发送内容增量
   */
  private sendContentDelta(content: string): void {
    const chunk: StreamChunk = {
      type: 'content',
      data: {
        content,
        messageId: this.context.messageId,
        conversationId: this.context.conversationId,
      },
    };

    this.sendEvent('message-delta', chunk);
  }

  /**
   * 发送完成事件
   */
  private async sendCompletionEvent(fullContent: string): Promise<void> {
    const endTime = Date.now();
    const duration = endTime - this.context.startTime;

    const completionData = {
      messageId: this.context.messageId,
      conversationId: this.context.conversationId,
      content: fullContent,
      tokens: this.tokenCount,
      model: this.context.model,
      duration,
      finishReason: 'stop',
      timestamp: new Date().toISOString(),
    };

    this.sendEvent('message-stop', completionData);

    // 异步保存消息到数据库
    this.saveMessageAsync(fullContent).catch(error => {
      console.error('Failed to save message:', error);
    });
  }

  /**
   * 发送错误事件
   */
  private sendError(message: string, details?: any): void {
    const errorData = {
      error: message,
      messageId: this.context.messageId,
      conversationId: this.context.conversationId,
      details,
      timestamp: new Date().toISOString(),
    };

    this.sendEvent('error', errorData);
  }

  /**
   * 发送SSE事件
   */
  private sendEvent(event: string, data: any): void {
    if (this.isEnded) return;

    try {
      const eventStr = this.formatSSEEvent(event, data);
      this.res.write(eventStr);
    } catch (error) {
      console.error('Failed to send SSE event:', error);
    }
  }

  /**
   * 格式化SSE事件
   */
  private formatSSEEvent(event: string, data: any): string {
    const eventId = randomUUID();
    let formatted = '';

    if (event) {
      formatted += `event: ${event}\n`;
    }

    formatted += `id: ${eventId}\n`;
    formatted += `data: ${JSON.stringify(data)}\n\n`;

    return formatted;
  }

  /**
   * 发送ping事件（保持连接）
   */
  private sendPing(): void {
    this.sendEvent('ping', { 
      timestamp: new Date().toISOString(),
      serverTime: Date.now(),
    });
  }

  /**
   * 刷新缓冲区
   */
  private async flushBuffer(): Promise<void> {
    if (this.buffer.length > 0) {
      // 这里可以添加缓冲区处理逻辑
      // 比如批量发送或者压缩
      this.buffer = '';
    }
  }

  /**
   * 异步保存消息到数据库
   */
  private async saveMessageAsync(content: string): Promise<void> {
    try {
      // 这里应该调用数据库保存逻辑
      // 暂时只是日志记录
      console.log('Message saved:', {
        messageId: this.context.messageId,
        conversationId: this.context.conversationId,
        userId: this.context.userId,
        contentLength: content.length,
        tokenCount: this.tokenCount,
      });
    } catch (error) {
      console.error('Failed to save message to database:', error);
    }
  }

  /**
   * 结束流式响应
   */
  end(): void {
    if (this.isEnded) return;
    
    this.isEnded = true;
    
    try {
      // 发送最终事件标记流结束
      this.res.write('event: done\ndata: {}\n\n');
      this.res.end();
    } catch (error) {
      console.error('Failed to end SSE stream:', error);
    }
  }

  /**
   * 处理连接中断
   */
  onConnectionClose(callback: () => void): void {
    this.res.on('close', () => {
      this.isEnded = true;
      callback();
    });

    this.res.on('error', (error) => {
      console.error('SSE connection error:', error);
      this.isEnded = true;
      callback();
    });
  }
}

/**
 * 创建流式处理器
 */
export function createStreamHandler(
  res: VercelResponse,
  context: StreamContext
): StreamHandler {
  return new StreamHandler(res, context);
}

/**
 * 处理OpenAI流式响应的工具函数
 */
export async function handleOpenAIStream(
  res: VercelResponse,
  stream: AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>,
  context: StreamContext
): Promise<void> {
  const handler = createStreamHandler(res, context);
  
  // 设置连接中断处理
  handler.onConnectionClose(() => {
    console.log('Client disconnected from SSE stream');
  });

  // 处理流式响应
  await handler.processStream(stream);
}

/**
 * 发送简单的SSE消息
 */
export function sendSSEMessage(
  res: VercelResponse,
  event: string,
  data: any,
  id?: string
): void {
  let message = '';
  
  if (event) {
    message += `event: ${event}\n`;
  }
  
  if (id) {
    message += `id: ${id}\n`;
  }
  
  message += `data: ${JSON.stringify(data)}\n\n`;
  
  res.write(message);
}

/**
 * 设置SSE响应头的工具函数
 */
export function setupSSEResponse(res: VercelResponse): void {
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no'); // Nginx缓冲控制
  
  // CORS headers for SSE
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
}

/**
 * 处理SSE预检请求
 */
export function handleSSEOptions(res: VercelResponse): void {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cache-Control');
  res.status(200).end();
}

/**
 * 检查客户端是否支持SSE
 */
export function clientSupportsSSE(req: VercelRequest): boolean {
  const accept = req.headers.accept || '';
  return accept.includes('text/event-stream');
}

/**
 * 创建SSE心跳
 */
export function createSSEHeartbeat(
  res: VercelResponse,
  intervalMs: number = 30000
): NodeJS.Timeout {
  return setInterval(() => {
    try {
      sendSSEMessage(res, 'heartbeat', {
        timestamp: new Date().toISOString(),
        serverTime: Date.now(),
      });
    } catch (error) {
      console.error('Heartbeat failed:', error);
    }
  }, intervalMs);
}

/**
 * 安全地清理SSE连接
 */
export function cleanupSSEConnection(
  res: VercelResponse,
  heartbeat?: NodeJS.Timeout
): void {
  if (heartbeat) {
    clearInterval(heartbeat);
  }

  try {
    if (!res.headersSent) {
      res.end();
    }
  } catch (error) {
    console.error('Failed to cleanup SSE connection:', error);
  }
}