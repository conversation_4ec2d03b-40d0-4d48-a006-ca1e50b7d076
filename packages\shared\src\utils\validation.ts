/**
 * HER 数据验证工具
 * 使用 Zod 进行类型安全的数据验证
 */

import { z } from 'zod';
import { MESSAGE_LIMITS, EMOTION_CONFIG } from '../constants/app-config';

// 用户验证模式
export const userValidationSchema = z.object({
  id: z.string().uuid(),
  phone: z.string().regex(/^1[3-9]\d{9}$/).optional(),
  nickname: z.string().min(1).max(20).optional(),
  avatarUrl: z.string().url().optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'auto']),
    notificationEnabled: z.boolean(),
    voiceEnabled: z.boolean(),
    language: z.string().min(2).max(5),
    emotionalAdaptation: z.boolean().default(true),
    dataRetention: z.number().int().min(1).max(365).default(30),
  }),
  isAnonymous: z.boolean(),
});

// 消息验证模式
export const messageValidationSchema = z.object({
  conversationId: z.string().uuid().optional(),
  content: z.string()
    .min(MESSAGE_LIMITS.MIN_LENGTH)
    .max(MESSAGE_LIMITS.MAX_LENGTH)
    .transform(str => str.trim()),
  emotion: z.enum([
    'neutral', 'warm', 'caring', 'listening', 
    'thinking', 'understanding', 'excited', 
    'concerned', 'supportive'
  ]).optional(),
});

// 记忆验证模式
export const memoryValidationSchema = z.object({
  type: z.enum(['fact', 'preference', 'event', 'emotion']),
  content: z.string().min(1).max(1000),
  importance: z.number().min(0).max(1).default(0.5),
  metadata: z.record(z.any()).optional(),
});

// 情绪签到验证模式
export const moodCheckInValidationSchema = z.object({
  moodValue: z.number()
    .int()
    .min(EMOTION_CONFIG.MOOD_SCALE_MIN)
    .max(EMOTION_CONFIG.MOOD_SCALE_MAX),
  moodLabel: z.string().min(1).max(50),
  note: z.string().max(500).optional(),
  triggers: z.array(z.string()).max(10).optional(),
});

// 分页参数验证模式
export const paginationValidationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

// 搜索参数验证模式
export const searchValidationSchema = z.object({
  query: z.string().min(1).max(200),
  limit: z.number().int().min(1).max(50).default(10),
  type: z.string().optional(),
  minImportance: z.number().min(0).max(1).default(0),
});

// 验证函数
export const validateUser = (data: unknown) => {
  return userValidationSchema.safeParse(data);
};

export const validateMessage = (data: unknown) => {
  return messageValidationSchema.safeParse(data);
};

export const validateMemory = (data: unknown) => {
  return memoryValidationSchema.safeParse(data);
};

export const validateMoodCheckIn = (data: unknown) => {
  return moodCheckInValidationSchema.safeParse(data);
};

export const validatePagination = (data: unknown) => {
  return paginationValidationSchema.safeParse(data);
};

export const validateSearch = (data: unknown) => {
  return searchValidationSchema.safeParse(data);
};

// 通用验证辅助函数
export const isValidUUID = (value: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
};

export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// 消息内容安全检查
export const isMessageSafe = (content: string): boolean => {
  // 检查是否包含恶意内容、过度重复等
  const suspiciousPatterns = [
    /(.)\1{20,}/, // 连续重复字符
    /<script/i, // 脚本标签
    /javascript:/i, // JavaScript 协议
    /data:text\/html/i, // HTML 数据 URL
  ];
  
  return !suspiciousPatterns.some(pattern => pattern.test(content));
};

// 情绪标签验证
export const isValidEmotionLabel = (label: string): boolean => {
  const validLabels = [
    'neutral', 'warm', 'caring', 'listening', 
    'thinking', 'understanding', 'excited', 
    'concerned', 'supportive'
  ];
  return validLabels.includes(label);
};