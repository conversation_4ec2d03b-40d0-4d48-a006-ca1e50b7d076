/**
 * /api/chat/send - 聊天消息发送API
 * 使用方法：POST /api/chat/send 发送消息并获得AI流式回复
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { randomUUID } from 'crypto';

// 导入自定义模块
import { withAuth, AuthenticatedRequest } from '../../middleware/auth';
import { withHerCors } from '../../middleware/cors';
import { withErrorHandler, combineMiddlewares } from '../../middleware/error-handler';
import { withChatRateLimit } from '../../lib/rate-limiter';
import { createErrorHandler, createAPIError, retryAsync } from '../../lib/error-utils';
import { createAIClientFromEnv } from '../../src/lib/ai-provider';
import { createContextManager, HER_CONTEXT_CONFIG } from '../../lib/context-manager';
import { handleOpenAIStream, setupSSEResponse, clientSupportsSSE } from './stream-handler';
import { SendMessageRequestSchema } from '../../types/chat';
import type { SendMessageRequest, StreamContext } from '../../types/chat';

/**
 * 聊天发送处理函数
 */
async function chatSendHandler(req: AuthenticatedRequest, res: VercelResponse): Promise<void> {
  // 只允许POST请求
  if (req.method !== 'POST') {
    throw APIErrors.BAD_REQUEST(`不支持的HTTP方法: ${req.method}`);
  }

  // 检查客户端是否支持SSE
  if (!clientSupportsSSE(req)) {
    throw APIErrors.BAD_REQUEST('客户端不支持Server-Sent Events');
  }

  try {
    // 验证请求数据
    const validatedData = SendMessageRequestSchema.parse(req.body);
    const { conversationId, message, metadata } = validatedData;

    // 设置SSE响应头
    setupSSEResponse(res);

    // 初始化服务
    const aiClient = createAIClientFromEnv();
    const contextManager = createContextManager(HER_CONTEXT_CONFIG);

    // 生成消息ID
    const messageId = randomUUID();
    const actualConversationId = conversationId || randomUUID();

    // 创建流式上下文
    const streamContext: StreamContext = {
      messageId,
      conversationId: actualConversationId,
      userId: req.userId,
      model: process.env.OPENROUTER_MODEL || 'openai/gpt-4-turbo',
      startTime: Date.now(),
    };

    console.log('Processing chat request:', {
      userId: req.userId,
      conversationId: actualConversationId,
      messageLength: message.length,
      model: streamContext.model,
    });

    // 构建对话上下文
    const contextMessages = await contextManager.buildContext(
      req.userId,
      conversationId,
      message
    );

    console.log('Context built:', {
      messageCount: contextMessages.length,
      totalLength: contextMessages.reduce((acc, msg) => acc + msg.content.length, 0),
    });

    // 调用OpenRouter流式API
    const stream = await aiClient.chatStream(contextMessages, {
      temperature: HER_CONTEXT_CONFIG.temperature,
      maxTokens: 1000,
    });

    // 处理流式响应
    await handleOpenAIStream(res, stream, streamContext);

  } catch (error: any) {
    console.error('Chat send error:', error);

    // 如果响应头还没发送，发送错误响应
    if (!res.headersSent) {
      if (error.name === 'ZodError') {
        throw APIErrors.VALIDATION_ERROR('请求参数验证失败', error.errors);
      }
      
      if (error.message?.includes('rate limit') || error.message?.includes('quota')) {
        throw APIErrors.AI_SERVICE_ERROR('AI服务暂时不可用，请稍后重试');
      }

      if (error.message?.includes('context_length')) {
        throw APIErrors.BAD_REQUEST('对话内容过长，请开始新的对话');
      }

      // 重新抛出错误让错误处理中间件处理
      throw error;
    } else {
      // 如果响应已开始，通过SSE发送错误
      try {
        res.write(`event: error\ndata: ${JSON.stringify({
          error: error.message || '处理消息时出现错误',
          messageId: messageId || null,
          conversationId: actualConversationId || null,
          timestamp: new Date().toISOString(),
        })}\n\n`);
        res.end();
      } catch (writeError) {
        console.error('Failed to send error via SSE:', writeError);
      }
    }
  }
}

/**
 * 导出带中间件的处理函数
 */
export default combineMiddlewares(
  chatSendHandler,
  [
    withErrorHandler,      // 错误处理
    withHerCors,          // CORS支持
    withAuth,             // JWT认证
    withChatRateLimit,    // 聊天速率限制
  ]
);

// 导出处理函数供测试使用
export { chatSendHandler };