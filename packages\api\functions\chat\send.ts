/**
 * /api/chat/send - 聊天消息发送API
 * 使用方法：POST /api/chat/send 发送消息并获得AI流式回复
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { randomUUID } from 'crypto';

// 导入自定义模块
import { withAuth, AuthenticatedRequest } from '../../middleware/auth';
import { withHerCors } from '../../middleware/cors';
import { withErrorHandler, combineMiddlewares } from '../../middleware/error-handler';
import { withChatRateLimit } from '../../lib/rate-limiter';
import { createErrorHandler, createAPIError, retryAsync } from '../../lib/error-utils';
import { createAIClientFromEnv } from '../../src/lib/ai-provider';
import { createContextManager, HER_CONTEXT_CONFIG } from '../../lib/context-manager';
import { handleOpenAIStream, setupSSEResponse, clientSupportsSSE } from './stream-handler';
import { SendMessageRequestSchema } from '../../types/chat';
import type { SendMessageRequest, StreamContext } from '../../types/chat';

/**
 * 聊天发送处理函数
 */
async function chatSendHandler(req: AuthenticatedRequest, res: VercelResponse): Promise<void> {
  // 创建错误处理器
  const errorHandler = createErrorHandler('chat-send', req.userId, (req as any).requestId);
  
  // 只允许POST请求
  if (req.method !== 'POST') {
    throw createAPIError.badRequest(`不支持的HTTP方法: ${req.method}`);
  }

  // 检查客户端是否支持SSE
  if (!clientSupportsSSE(req)) {
    throw createAPIError.badRequest('客户端不支持Server-Sent Events');
  }

  try {
    // 验证请求数据
    const validatedData = SendMessageRequestSchema.parse(req.body);
    const { conversationId, message, metadata } = validatedData;

    // 设置SSE响应头
    setupSSEResponse(res);

    // 初始化服务
    const aiClient = createAIClientFromEnv();
    const contextManager = createContextManager(HER_CONTEXT_CONFIG);

    // 生成消息ID
    const messageId = randomUUID();
    const actualConversationId = conversationId || randomUUID();

    // 创建流式上下文
    const streamContext: StreamContext = {
      messageId,
      conversationId: actualConversationId,
      userId: req.userId,
      model: process.env.OPENROUTER_MODEL || 'openai/gpt-4-turbo',
      startTime: Date.now(),
    };

    console.log('Processing chat request:', {
      userId: req.userId,
      conversationId: actualConversationId,
      messageLength: message.length,
      model: streamContext.model,
    });

    // 构建对话上下文
    const contextMessages = await contextManager.buildContext(
      req.userId,
      conversationId,
      message
    );

    console.log('Context built:', {
      messageCount: contextMessages.length,
      totalLength: contextMessages.reduce((acc, msg) => acc + msg.content.length, 0),
    });

    // 调用OpenRouter流式API (使用重试机制)
    const stream = await retryAsync(
      () => aiClient.chatStream(contextMessages, {
        temperature: HER_CONTEXT_CONFIG.temperature,
        maxTokens: 1000,
      }),
      2, // 最多重试2次
      1000 // 延迟1秒
    );

    // 处理流式响应
    await handleOpenAIStream(res, stream, streamContext);

  } catch (error: any) {
    // 使用错误处理器
    const standardError = errorHandler.handle(error);

    // 如果响应头还没发送，重新抛出让中间件处理
    if (!res.headersSent) {
      throw standardError;
    } else {
      // 如果响应已开始，通过SSE发送错误
      try {
        res.write(`event: error\ndata: ${JSON.stringify({
          error: standardError.message,
          code: standardError.code,
          messageId: messageId || null,
          conversationId: actualConversationId || null,
          timestamp: new Date().toISOString(),
        })}\n\n`);
        res.end();
      } catch (writeError) {
        console.error('Failed to send error via SSE:', writeError);
      }
    }
  }
}

/**
 * 导出带中间件的处理函数
 */
export default combineMiddlewares(
  chatSendHandler,
  [
    withErrorHandler,      // 错误处理
    withHerCors,          // CORS支持
    withAuth,             // JWT认证
    withChatRateLimit,    // 聊天速率限制
  ]
);

// 导出处理函数供测试使用
export { chatSendHandler };