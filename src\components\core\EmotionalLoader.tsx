/**
 * EmotionalLoader Component
 * 情感化的加载动画 - 让等待也充满温度
 */

import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Easing,
  Dimensions,
} from 'react-native';
import { defaultTheme, emotionalColors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

const { width: screenWidth } = Dimensions.get('window');

export interface EmotionalLoaderProps {
  type?: 'listening' | 'thinking' | 'understanding' | 'connecting';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  message?: string;
  showMessage?: boolean;
}

const EmotionalLoader: React.FC<EmotionalLoaderProps> = ({
  type = 'listening',
  size = 'medium',
  color,
  message,
  showMessage = true,
}) => {
  // 动画值
  const pulseAnim = useRef(new Animated.Value(0)).current;
  const rippleAnim1 = useRef(new Animated.Value(0)).current;
  const rippleAnim2 = useRef(new Animated.Value(0)).current;
  const rippleAnim3 = useRef(new Animated.Value(0)).current;
  const breathAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  // 获取尺寸
  const getSizes = () => {
    const baseSize = {
      small: 40,
      medium: 60,
      large: 80,
    }[size];

    return {
      container: baseSize,
      core: baseSize * 0.4,
      ripple: baseSize * 1.5,
    };
  };

  const sizes = getSizes();

  // 获取颜色
  const getColor = () => {
    if (color) return color;
    
    switch (type) {
      case 'listening':
        return emotionalColors.listening.accent;
      case 'thinking':
        return emotionalColors.thinking.accent;
      case 'understanding':
        return emotionalColors.understanding.accent;
      case 'connecting':
        return defaultTheme.accent;
      default:
        return defaultTheme.bubble.her;
    }
  };

  const loaderColor = getColor();

  // 获取消息文本
  const getMessage = () => {
    if (message) return message;
    
    switch (type) {
      case 'listening':
        return '正在倾听...';
      case 'thinking':
        return '思考中...';
      case 'understanding':
        return '理解中...';
      case 'connecting':
        return '连接中...';
      default:
        return '';
    }
  };

  useEffect(() => {
    // 根据类型启动不同的动画
    switch (type) {
      case 'listening':
        // 涟漪扩散动画
        startRippleAnimation();
        break;
      case 'thinking':
        // 呼吸光晕动画
        startBreathingAnimation();
        break;
      case 'understanding':
        // 脉冲动画
        startPulseAnimation();
        break;
      case 'connecting':
        // 旋转连接动画
        startRotatingAnimation();
        break;
    }

    return () => {
      // 清理动画
      pulseAnim.stopAnimation();
      rippleAnim1.stopAnimation();
      rippleAnim2.stopAnimation();
      rippleAnim3.stopAnimation();
      breathAnim.stopAnimation();
      rotateAnim.stopAnimation();
    };
  }, [type]);

  // 涟漪动画（倾听）
  const startRippleAnimation = () => {
    const createRipple = (anim: Animated.Value, delay: number) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.parallel([
            Animated.timing(anim, {
              toValue: 1,
              duration: 2000,
              easing: Easing.out(Easing.ease),
              useNativeDriver: true,
            }),
          ]),
          Animated.timing(anim, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      );
    };

    Animated.parallel([
      createRipple(rippleAnim1, 0),
      createRipple(rippleAnim2, 600),
      createRipple(rippleAnim3, 1200),
    ]).start();
  };

  // 呼吸动画（思考）
  const startBreathingAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(breathAnim, {
          toValue: 1.3,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(breathAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // 脉冲动画（理解）
  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0,
          duration: 800,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  // 旋转动画（连接）
  const startRotatingAnimation = () => {
    Animated.loop(
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 3000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    ).start();
  };

  // 渲染涟漪（倾听状态）
  const renderRipples = () => {
    const renderRipple = (anim: Animated.Value, key: string) => (
      <Animated.View
        key={key}
        style={[
          styles.ripple,
          {
            width: sizes.ripple,
            height: sizes.ripple,
            borderRadius: sizes.ripple / 2,
            borderColor: loaderColor,
            opacity: anim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.8, 0],
            }),
            transform: [{
              scale: anim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.3, 1],
              }),
            }],
          },
        ]}
      />
    );

    return (
      <>
        {renderRipple(rippleAnim1, 'ripple1')}
        {renderRipple(rippleAnim2, 'ripple2')}
        {renderRipple(rippleAnim3, 'ripple3')}
      </>
    );
  };

  // 渲染呼吸光晕（思考状态）
  const renderBreathing = () => (
    <Animated.View
      style={[
        styles.breathingCore,
        {
          width: sizes.core,
          height: sizes.core,
          borderRadius: sizes.core / 2,
          backgroundColor: loaderColor,
          transform: [{
            scale: breathAnim,
          }],
          opacity: breathAnim.interpolate({
            inputRange: [1, 1.3],
            outputRange: [0.8, 0.4],
          }),
        },
      ]}
    />
  );

  // 渲染脉冲（理解状态）
  const renderPulse = () => (
    <>
      <Animated.View
        style={[
          styles.pulseOuter,
          {
            width: sizes.container,
            height: sizes.container,
            borderRadius: sizes.container / 2,
            backgroundColor: loaderColor,
            opacity: pulseAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0, 0.3],
            }),
            transform: [{
              scale: pulseAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1.2],
              }),
            }],
          },
        ]}
      />
      <View
        style={[
          styles.pulseInner,
          {
            width: sizes.core,
            height: sizes.core,
            borderRadius: sizes.core / 2,
            backgroundColor: loaderColor,
          },
        ]}
      />
    </>
  );

  // 渲染旋转（连接状态）
  const renderRotating = () => (
    <Animated.View
      style={[
        styles.rotatingContainer,
        {
          width: sizes.container,
          height: sizes.container,
          transform: [{
            rotate: rotateAnim.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '360deg'],
            }),
          }],
        },
      ]}
    >
      <View
        style={[
          styles.rotatingDot,
          {
            width: sizes.core / 2,
            height: sizes.core / 2,
            borderRadius: sizes.core / 4,
            backgroundColor: loaderColor,
          },
        ]}
      />
      <View
        style={[
          styles.rotatingDot,
          styles.rotatingDotOpposite,
          {
            width: sizes.core / 2,
            height: sizes.core / 2,
            borderRadius: sizes.core / 4,
            backgroundColor: loaderColor,
            opacity: 0.5,
          },
        ]}
      />
    </Animated.View>
  );

  // 根据类型渲染不同的动画
  const renderAnimation = () => {
    switch (type) {
      case 'listening':
        return renderRipples();
      case 'thinking':
        return renderBreathing();
      case 'understanding':
        return renderPulse();
      case 'connecting':
        return renderRotating();
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.animationContainer, { width: sizes.container, height: sizes.container }]}>
        {renderAnimation()}
      </View>
      {showMessage && (
        <Text style={[styles.message, { color: loaderColor }]}>
          {getMessage()}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.lg,
  },
  
  animationContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // 涟漪样式
  ripple: {
    position: 'absolute',
    borderWidth: 2,
  },
  
  // 呼吸样式
  breathingCore: {
    position: 'absolute',
  },
  
  // 脉冲样式
  pulseOuter: {
    position: 'absolute',
  },
  
  pulseInner: {
    position: 'absolute',
  },
  
  // 旋转样式
  rotatingContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  rotatingDot: {
    position: 'absolute',
    top: 0,
  },
  
  rotatingDotOpposite: {
    bottom: 0,
    top: undefined,
  },
  
  // 消息文本
  message: {
    ...textStyles.chatStatus,
    marginTop: spacing.md,
  },
});

export default EmotionalLoader;