/**
 * HER 主题提供器
 * 为应用提供统一的主题上下文
 */

import React, { createContext, useContext, useMemo, ReactNode } from 'react';
import { designTokens, ThemeContext as IThemeContext, ThemeVariant, ThemeMode } from '@her/shared';

interface ThemeProviderProps {
  children: ReactNode;
  variant?: ThemeVariant;
  mode?: ThemeMode;
}

// 创建主题上下文
const ThemeContext = createContext<IThemeContext | undefined>(undefined);

// 构建完整的主题对象
const buildThemeContext = (variant: ThemeVariant, mode: ThemeMode): IThemeContext => {
  const themeConfig = designTokens.themes[variant];
  const isDark = mode === 'dark' || (mode === 'auto' && isSystemDarkMode());
  
  // 如果是auto模式且当前主题不匹配系统模式，选择合适的主题
  let actualVariant = variant;
  if (mode === 'auto') {
    actualVariant = isDark ? 'night' : 'default';
  }
  
  const actualThemeConfig = designTokens.themes[actualVariant];
  
  return {
    colors: {
      // 使用主题配置的颜色，回退到默认值
      primary: actualThemeConfig.colors?.primary || designTokens.colors.morningCream,
      secondary: actualThemeConfig.colors?.secondary || designTokens.colors.twilightPink,
      background: actualThemeConfig.colors?.background || designTokens.colors.morningCream,
      surface: actualThemeConfig.colors?.surface || designTokens.colors.white,
      text: {
        primary: actualThemeConfig.colors?.text?.primary || designTokens.colors.gray[800],
        secondary: actualThemeConfig.colors?.text?.secondary || designTokens.colors.gray[600],
        disabled: actualThemeConfig.colors?.text?.disabled || designTokens.colors.gray[400],
      },
      bubble: {
        her: actualThemeConfig.colors?.bubble?.her || designTokens.colors.twilightPink,
        user: actualThemeConfig.colors?.bubble?.user || designTokens.colors.moonlightBlue,
      },
      accent: actualThemeConfig.colors?.accent || designTokens.colors.candleOrange,
      border: actualThemeConfig.colors?.border || `${designTokens.colors.gray[200]}40`,
      shadow: actualThemeConfig.colors?.shadow || 'rgba(0, 0, 0, 0.08)',
    },
    typography: designTokens.typography,
    spacing: designTokens.spacing,
    borderRadius: designTokens.borderRadius,
    shadows: designTokens.shadows,
    animation: designTokens.animation,
    mode,
    variant: actualVariant,
    isDark,
  };
};

// 检测系统是否为深色模式
const isSystemDarkMode = (): boolean => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  
  // React Native 中可能需要使用 Appearance API
  // import { Appearance } from 'react-native';
  // return Appearance.getColorScheme() === 'dark';
  
  return false; // 默认浅色模式
};

// 主题提供器组件
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ 
  children, 
  variant = 'default', 
  mode = 'auto' 
}) => {
  const themeContext = useMemo(() => 
    buildThemeContext(variant, mode), 
    [variant, mode]
  );

  return (
    <ThemeContext.Provider value={themeContext}>
      {children}
    </ThemeContext.Provider>
  );
};

// 主题Hook
export const useTheme = (): IThemeContext => {
  const context = useContext(ThemeContext);
  
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  
  return context;
};

// 主题工具函数
export const getThemeColors = (variant: ThemeVariant = 'default', mode: ThemeMode = 'light') => {
  return buildThemeContext(variant, mode).colors;
};

// 时间感知主题选择器
export const getTimeBasedTheme = (): ThemeVariant => {
  const hour = new Date().getHours();
  
  if (hour >= 6 && hour < 12) {
    return 'default'; // 早晨
  } else if (hour >= 12 && hour < 18) {
    return 'peaceful'; // 下午
  } else if (hour >= 18 && hour < 22) {
    return 'warm'; // 傍晚
  } else {
    return 'night'; // 深夜
  }
};

// 情绪感知主题选择器
export const getEmotionBasedTheme = (emotion: string): ThemeVariant => {
  switch (emotion) {
    case 'calm':
    case 'peaceful':
      return 'peaceful';
    case 'excited':
    case 'happy':
      return 'warm';
    case 'sad':
    case 'tired':
      return 'night';
    default:
      return 'default';
  }
};