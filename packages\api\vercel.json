{"version": 2, "name": "her-api", "builds": [{"src": "api/**/*.ts", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}], "functions": {"api/**/*.ts": {"runtime": "nodejs20.x", "maxDuration": 30}}, "env": {"NODE_ENV": "production"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Request-ID"}]}]}