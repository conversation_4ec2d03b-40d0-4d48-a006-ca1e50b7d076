/**
 * HER Authentication Service
 * 认证相关API服务
 */

import { 
  AnonymousLoginResponse, 
  PhoneLoginRequest, 
  PhoneLoginResponse,
  User,
  AUTH_ENDPOINTS 
} from '@her/shared';
import { apiClient } from './apiClient';

class AuthService {
  // 匿名登录
  async loginAnonymously(): Promise<AnonymousLoginResponse> {
    const response = await apiClient.post<AnonymousLoginResponse>(
      AUTH_ENDPOINTS.ANONYMOUS,
      {}
    );
    return response.data;
  }
  
  // 手机号登录
  async loginWithPhone(request: PhoneLoginRequest): Promise<PhoneLoginResponse> {
    const response = await apiClient.post<PhoneLoginResponse>(
      AUTH_ENDPOINTS.PHONE,
      request
    );
    return response.data;
  }
  
  // 发送验证码
  async sendVerificationCode(phone: string): Promise<void> {
    await apiClient.post(AUTH_ENDPOINTS.VERIFY, { phone });
  }
  
  // 刷新令牌
  async refreshToken(): Promise<{ token: string; user: User }> {
    const response = await apiClient.post<{ token: string; user: User }>(
      AUTH_ENDPOINTS.REFRESH,
      {}
    );
    return response.data;
  }
  
  // 退出登录
  async logout(): Promise<void> {
    await apiClient.post(AUTH_ENDPOINTS.LOGOUT, {});
  }
  
  // 验证令牌
  async verifyToken(): Promise<User> {
    const response = await apiClient.get<User>(AUTH_ENDPOINTS.VERIFY);
    return response.data;
  }
  
  // 更新用户资料
  async updateProfile(updates: Partial<User>): Promise<User> {
    const response = await apiClient.patch<User>(
      '/api/user/profile',
      updates
    );
    return response.data;
  }
  
  // 删除账户
  async deleteAccount(): Promise<void> {
    await apiClient.delete('/api/user/delete');
  }
}

export const authService = new AuthService();