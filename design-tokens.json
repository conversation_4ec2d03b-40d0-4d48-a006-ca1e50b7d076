{"$schema": "https://json-schema.org/draft-07/schema", "$id": "HER-design-tokens", "name": "HER Design Tokens", "description": "HER应用的设计令牌系统 - 跨平台统一的设计变量", "version": "1.0.0", "lastModified": "2025-01-09", "colors": {"base": {"morningCream": {"value": "#FAF7F2", "description": "晨曦米白 - 主背景色"}, "twilightPink": {"value": "#F5E6E0", "description": "暮光粉 - HER消息气泡"}, "moonlightBlue": {"value": "#E8EEF5", "description": "月光蓝 - 用户消息气泡"}, "candleOrange": {"value": "#FFE5D4", "description": "烛光橙 - 强调色"}, "midnightBlue": {"value": "#2C3E50", "description": "午夜蓝 - 深色主题主色"}, "deepNavy": {"value": "#34495E", "description": "深海军蓝 - 深色主题次色"}}, "semantic": {"success": {"value": "#95C99F", "description": "柔和绿 - 成功/积极"}, "warning": {"value": "#F4C67F", "description": "温柔黄 - 警告/提醒"}, "error": {"value": "#E8A5A5", "description": "柔和红 - 错误/危险"}, "info": {"value": "#A5C4E8", "description": "淡蓝 - 信息/中性"}}, "gray": {"50": {"value": "#FAFAFA"}, "100": {"value": "#F5F5F5"}, "200": {"value": "#E5E5E5"}, "300": {"value": "#D4D4D4"}, "400": {"value": "#A3A3A3"}, "500": {"value": "#737373"}, "600": {"value": "#525252"}, "700": {"value": "#404040"}, "800": {"value": "#262626"}, "900": {"value": "#171717"}}, "emotional": {"listening": {"primary": "#E8F4F8", "accent": "#7FC4D6", "bubble": "#D6E9F0"}, "thinking": {"primary": "#F8F4E8", "accent": "#D6C47F", "bubble": "#F0E9D6"}, "caring": {"primary": "#F8E8F4", "accent": "#D67FA6", "bubble": "#F0D6E9"}, "understanding": {"primary": "#E8F8E8", "accent": "#7FD67F", "bubble": "#D6F0D6"}}}, "typography": {"fontFamilies": {"chinese": {"value": "PingFangSC, NotoSansCJK, sans-serif", "description": "中文字体栈"}, "english": {"value": "Inter, -apple-system, sans-serif", "description": "英文字体栈"}, "chat": {"value": "Nunito, PingFangSC, sans-serif", "description": "对话专用字体"}}, "fontSizes": {"largeTitle": {"value": 28, "unit": "px"}, "title1": {"value": 24, "unit": "px"}, "title2": {"value": 20, "unit": "px"}, "title3": {"value": 18, "unit": "px"}, "large": {"value": 17, "unit": "px"}, "body": {"value": 16, "unit": "px"}, "callout": {"value": 15, "unit": "px"}, "subhead": {"value": 14, "unit": "px"}, "footnote": {"value": 13, "unit": "px"}, "caption1": {"value": 12, "unit": "px"}, "caption2": {"value": 11, "unit": "px"}}, "lineHeights": {"tight": {"value": 1.2}, "normal": {"value": 1.5}, "relaxed": {"value": 1.8}, "loose": {"value": 2.0}}, "fontWeights": {"light": {"value": 300}, "regular": {"value": 400}, "medium": {"value": 500}, "semibold": {"value": 600}, "bold": {"value": 700}}}, "spacing": {"scale": {"xxs": {"value": 4, "unit": "px"}, "xs": {"value": 8, "unit": "px"}, "sm": {"value": 12, "unit": "px"}, "md": {"value": 16, "unit": "px"}, "lg": {"value": 24, "unit": "px"}, "xl": {"value": 32, "unit": "px"}, "xxl": {"value": 48, "unit": "px"}, "xxxl": {"value": 64, "unit": "px"}}, "chat": {"betweenMessages": {"value": 12, "unit": "px"}, "bubblePaddingH": {"value": 16, "unit": "px"}, "bubblePaddingV": {"value": 12, "unit": "px"}, "inputPadding": {"value": 16, "unit": "px"}}}, "borderRadius": {"xs": {"value": 4, "unit": "px"}, "sm": {"value": 8, "unit": "px"}, "md": {"value": 12, "unit": "px"}, "lg": {"value": 16, "unit": "px"}, "xl": {"value": 24, "unit": "px"}, "pill": {"value": 999, "unit": "px"}, "bubble": {"value": 16, "unit": "px", "description": "对话气泡圆角"}, "button": {"value": 12, "unit": "px", "description": "按钮圆角"}, "card": {"value": 16, "unit": "px", "description": "卡片圆角"}, "input": {"value": 24, "unit": "px", "description": "输入框圆角"}}, "shadows": {"xs": {"color": "rgba(0, 0, 0, 0.05)", "offsetX": 0, "offsetY": 1, "blur": 2, "spread": 0}, "sm": {"color": "rgba(0, 0, 0, 0.08)", "offsetX": 0, "offsetY": 2, "blur": 4, "spread": 0}, "md": {"color": "rgba(0, 0, 0, 0.10)", "offsetX": 0, "offsetY": 4, "blur": 8, "spread": 0}, "lg": {"color": "rgba(0, 0, 0, 0.12)", "offsetX": 0, "offsetY": 8, "blur": 16, "spread": 0}, "bubble": {"color": "rgba(0, 0, 0, 0.08)", "offsetX": 0, "offsetY": 2, "blur": 8, "spread": 0, "description": "对话气泡阴影"}}, "animation": {"duration": {"instant": {"value": 100, "unit": "ms"}, "fast": {"value": 200, "unit": "ms"}, "normal": {"value": 300, "unit": "ms"}, "slow": {"value": 500, "unit": "ms"}, "verySlow": {"value": 1000, "unit": "ms"}, "breathing": {"value": 2000, "unit": "ms", "description": "呼吸动画周期"}}, "easing": {"linear": "linear", "easeIn": "cubic-bezier(0.4, 0, 1, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)", "spring": "cubic-bezier(0.175, 0.885, 0.32, 1.275)"}}, "layout": {"maxContentWidth": {"value": 600, "unit": "px"}, "maxBubbleWidth": {"value": 0.75, "unit": "ratio", "description": "气泡最大宽度比例"}, "minTouchTarget": {"value": 44, "unit": "px", "description": "最小触摸目标"}, "headerHeight": {"value": 56, "unit": "px"}, "tabBarHeight": {"value": 49, "unit": "px"}}, "iconSizes": {"xs": {"value": 16, "unit": "px"}, "sm": {"value": 20, "unit": "px"}, "md": {"value": 24, "unit": "px"}, "lg": {"value": 28, "unit": "px"}, "xl": {"value": 32, "unit": "px"}}, "avatarSizes": {"xs": {"value": 24, "unit": "px"}, "sm": {"value": 32, "unit": "px"}, "md": {"value": 40, "unit": "px"}, "lg": {"value": 48, "unit": "px"}, "xl": {"value": 56, "unit": "px"}}, "themes": {"default": {"name": "默认主题", "mode": "light", "colors": {"background": "$colors.base.morningCream", "surface": "#FFFFFF", "textPrimary": "$colors.gray.800", "textSecondary": "$colors.gray.600", "bubbleHer": "$colors.base.twilightPink", "bubbleUser": "$colors.base.moonlightBlue", "accent": "$colors.base.candleOrange"}}, "peaceful": {"name": "宁静模式", "mode": "light", "colors": {"background": "#F0F4F8", "surface": "#FFFFFF", "textPrimary": "$colors.gray.800", "textSecondary": "$colors.gray.600", "bubbleHer": "#E0E8F5", "bubbleUser": "#D4E2F5", "accent": "$colors.semantic.info"}}, "warm": {"name": "温暖模式", "mode": "light", "colors": {"background": "#FFF9F5", "surface": "#FFFFFF", "textPrimary": "$colors.gray.800", "textSecondary": "$colors.gray.600", "bubbleHer": "$colors.base.candleOrange", "bubbleUser": "$colors.base.twilightPink", "accent": "#FFA07A"}}, "night": {"name": "深夜模式", "mode": "dark", "colors": {"background": "$colors.gray.900", "surface": "$colors.gray.800", "textPrimary": "$colors.gray.100", "textSecondary": "$colors.gray.300", "bubbleHer": "$colors.base.deepNavy", "bubbleUser": "#3A506B", "accent": "#5A7A9A"}}}, "breakpoints": {"mobile": {"value": 0, "unit": "px"}, "tablet": {"value": 768, "unit": "px"}, "desktop": {"value": 1024, "unit": "px"}}, "platforms": {"ios": {"statusBarHeight": 44, "homeIndicatorHeight": 34, "fontFamily": "PingFangSC"}, "android": {"statusBarHeight": 24, "navigationBarHeight": 48, "fontFamily": "NotoSansCJK"}}}