#!/bin/bash

# HER 项目初始化脚本

set -e

echo "🌸 HER 项目初始化开始..."

# 检查必需的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "🔍 检查必需工具..."
check_tool "node"
check_tool "pnpm"
check_tool "git"

# 检查Node.js版本
node_version=$(node --version | cut -d'.' -f1 | cut -d'v' -f2)
if [ "$node_version" -lt 20 ]; then
    echo "❌ Node.js版本需要20或更高，当前版本: $(node --version)"
    exit 1
else
    echo "✅ Node.js版本满足要求: $(node --version)"
fi

# 安装依赖
echo "📦 安装依赖..."
pnpm install

# 构建共享包
echo "🔨 构建共享包..."
pnpm shared:build
pnpm ui:build

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "📄 创建环境变量文件..."
    cp .env.example .env.local
    echo "⚠️  请编辑 .env.local 文件，填入正确的API密钥和配置"
fi

# iOS特定设置（仅在macOS上）
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 检测到macOS，准备iOS开发环境..."
    if command -v xcodebuild &> /dev/null; then
        echo "✅ Xcode已安装"
        
        # 安装iOS依赖
        if [ -d "apps/mobile/ios" ]; then
            echo "📱 安装iOS依赖..."
            cd apps/mobile/ios
            if command -v pod &> /dev/null; then
                pod install
            else
                echo "⚠️  CocoaPods未安装，请运行: sudo gem install cocoapods"
            fi
            cd ../../..
        fi
    else
        echo "⚠️  Xcode未安装，无法进行iOS开发"
    fi
fi

# Android特定设置
if command -v adb &> /dev/null; then
    echo "🤖 Android SDK已安装"
else
    echo "⚠️  Android SDK未安装，请安装Android Studio"
fi

echo ""
echo "🎉 HER 项目初始化完成！"
echo ""
echo "📝 接下来的步骤:"
echo "1. 编辑 .env.local 文件，配置API密钥"
echo "2. 运行 'pnpm dev' 启动开发服务器"
echo "3. 运行 'pnpm mobile:ios' 或 'pnpm mobile:android' 启动移动应用"
echo ""
echo "📚 更多信息请查看 README.md"