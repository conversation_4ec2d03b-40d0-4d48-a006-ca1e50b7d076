# OpenRouter 配置指南

## 🌟 为什么选择 OpenRouter？

OpenRouter 是一个统一的 AI 模型网关，优势：
- ✅ **多模型支持** - GPT-4、<PERSON> 3、Llama 3、Gemini 等
- ✅ **成本优化** - 自动选择最便宜的提供商
- ✅ **无需多个 API Key** - 一个 key 访问所有模型
- ✅ **按需付费** - 无月费，用多少付多少
- ✅ **稳定性高** - 自动故障转移

## 📝 注册步骤

### 1. 创建账号
1. 访问 https://openrouter.ai
2. 点击 "Sign Up" 注册
3. 可以用 Google 账号快速登录

### 2. 获取 API Key
1. 登录后访问 https://openrouter.ai/keys
2. 点击 "Create Key"
3. 给 Key 命名（如 "HER-App"）
4. 复制生成的 key（格式：`sk-or-v1-xxxxx`）

### 3. 充值积分（可选）
1. 访问 https://openrouter.ai/credits
2. 最低充值 $5
3. 支持信用卡、PayPal

## ⚙️ 配置 HER 项目

### 1. 更新环境变量

编辑 `.env.local`：

```bash
# AI 配置
AI_PROVIDER=openrouter
OPENROUTER_API_KEY=sk-or-v1-你的密钥
OPENROUTER_MODEL=openai/gpt-4-turbo-preview
```

### 2. 模型选择建议

根据不同场景选择模型：

#### 💬 日常对话（平衡性能和成本）
```bash
OPENROUTER_MODEL=openai/gpt-3.5-turbo
# 成本: $0.0005/1K输入, $0.0015/1K输出
# 特点: 快速、便宜、效果好
```

#### 🎭 情感理解（高质量）
```bash
OPENROUTER_MODEL=anthropic/claude-3-sonnet
# 成本: $0.003/1K输入, $0.015/1K输出
# 特点: 出色的情感理解和共情能力
```

#### 💰 成本优先（开源模型）
```bash
OPENROUTER_MODEL=meta-llama/llama-3-70b-instruct
# 成本: $0.0007/1K输入, $0.0009/1K输出
# 特点: 开源、便宜、中文能力较好
```

#### ⚡ 响应速度优先
```bash
OPENROUTER_MODEL=anthropic/claude-3-haiku
# 成本: $0.00025/1K输入, $0.00125/1K输出
# 特点: 极快的响应速度，适合实时对话
```

## 🔧 API 使用示例

### 基础对话请求

```typescript
// 使用我们封装好的 AI 客户端
import { createAIClientFromEnv } from '@/lib/ai-provider';

const ai = createAIClientFromEnv();

// 发送消息
const response = await ai.chat([
  { role: 'system', content: 'You are HER, a warm and caring AI companion.' },
  { role: 'user', content: '今天感觉有点累' }
]);

console.log(response.choices[0].message.content);
```

### 流式响应（打字效果）

```typescript
// 流式对话
const stream = ai.chatStream([
  { role: 'user', content: '给我讲个温暖的故事' }
]);

for await (const chunk of stream) {
  process.stdout.write(chunk); // 实时输出
}
```

### 切换模型

```typescript
// 动态切换模型
import { createAIClient } from '@/lib/ai-provider';

// 使用 Claude 进行深度情感对话
const claudeAI = createAIClient({
  provider: 'openrouter',
  apiKey: process.env.OPENROUTER_API_KEY!,
  model: 'anthropic/claude-3-opus'
});

// 使用 Llama 进行日常聊天
const llamaAI = createAIClient({
  provider: 'openrouter',
  apiKey: process.env.OPENROUTER_API_KEY!,
  model: 'meta-llama/llama-3-70b-instruct'
});
```

## 💸 成本估算

以每天 100 条消息计算（每条约 100 tokens）：

| 模型 | 日成本 | 月成本 | 适用场景 |
|------|--------|--------|----------|
| GPT-3.5 Turbo | $0.02 | $0.60 | 日常对话 |
| Claude 3 Haiku | $0.01 | $0.30 | 快速响应 |
| Llama 3 70B | $0.016 | $0.48 | 平衡选择 |
| GPT-4 Turbo | $0.20 | $6.00 | 深度理解 |

## 🚨 注意事项

### 1. API 限制
- 默认限速：200 请求/分钟
- 可以申请提高限额

### 2. 隐私保护
- OpenRouter 不会存储对话内容
- 但请避免传输敏感个人信息

### 3. 错误处理
```typescript
try {
  const response = await ai.chat(messages);
} catch (error) {
  if (error.code === 'insufficient_credits') {
    console.log('余额不足，请充值');
  } else if (error.code === 'rate_limit_exceeded') {
    console.log('请求过快，请稍后重试');
  }
}
```

### 4. 监控使用量
- 在 https://openrouter.ai/activity 查看使用记录
- 设置使用提醒避免超支

## 🔄 切换到 OpenAI 直连

如果需要切换回 OpenAI 直连：

```bash
# .env.local
AI_PROVIDER=openai
OPENAI_API_KEY=sk-xxxxx
OPENAI_MODEL=gpt-4-turbo-preview
```

代码无需修改，自动适配！

## 📊 性能对比

| 特性 | OpenRouter | OpenAI 直连 |
|------|------------|-------------|
| 模型选择 | 多样 | 仅 OpenAI |
| 成本 | 灵活优化 | 固定价格 |
| 稳定性 | 高（多提供商） | 依赖单一服务 |
| 配置复杂度 | 简单 | 简单 |
| 中国可访问 | 需要代理 | 需要代理 |

## 🎯 推荐配置

对于 HER 项目，推荐：

```bash
# 主模型：平衡性能和成本
OPENROUTER_MODEL=openai/gpt-3.5-turbo

# 或使用 Claude 获得更好的情感理解
OPENROUTER_MODEL=anthropic/claude-3-haiku
```

---

现在你可以开始使用 OpenRouter 为 HER 提供 AI 能力了！🎉