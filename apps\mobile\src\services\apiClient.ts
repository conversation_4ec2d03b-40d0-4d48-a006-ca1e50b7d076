/**
 * HER API Client
 * 统一的API客户端配置
 */

import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS, ApiResponse, ApiError, Her<PERSON><PERSON><PERSON> } from '@her/shared';

// 创建axios实例
export const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  async (config) => {
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.warn('Failed to get auth token:', error);
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId();
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 成功响应直接返回
    return response;
  },
  async (error: AxiosError<ApiError>) => {
    const originalRequest = error.config;
    
    // 处理401未授权错误
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        // 尝试刷新token
        const refreshResponse = await apiClient.post('/auth/refresh');
        const { token } = refreshResponse.data;
        
        // 保存新token
        await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
        
        // 重试原始请求
        originalRequest.headers.Authorization = `Bearer ${token}`;
        return apiClient(originalRequest);
      } catch (refreshError) {
        // 刷新失败，清除token并重定向到登录页面
        await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        // 这里可以发送一个全局事件来通知应用重定向到登录页面
        console.error('Token refresh failed:', refreshError);
      }
    }
    
    // 转换为标准化错误
    const herError = convertToHerError(error);
    return Promise.reject(herError);
  }
);

// 将axios错误转换为HerError
const convertToHerError = (error: AxiosError<ApiError>): HerError => {
  if (error.response?.data?.error) {
    const apiError = error.response.data.error;
    return new HerError(
      apiError.message,
      apiError.code,
      error.response.status,
      apiError.details
    );
  }
  
  if (error.code === 'ECONNABORTED') {
    return new HerError(
      '请求超时，请检查网络连接',
      'NETWORK_TIMEOUT',
      408
    );
  }
  
  if (error.code === 'NETWORK_ERROR' || !error.response) {
    return new HerError(
      '网络连接失败，请检查网络设置',
      'NETWORK_CONNECTION_FAILED',
      0
    );
  }
  
  return new HerError(
    error.message || '未知错误',
    'UNKNOWN_ERROR',
    error.response?.status
  );
};

// 生成请求ID
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 设置认证token
export const setAuthToken = async (token: string): Promise<void> => {
  await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
  apiClient.defaults.headers.Authorization = `Bearer ${token}`;
};

// 清除认证token
export const clearAuthToken = async (): Promise<void> => {
  await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
  delete apiClient.defaults.headers.Authorization;
};

// 检查网络连接状态
export const checkNetworkConnection = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health', { timeout: 5000 });
    return response.status === 200;
  } catch {
    return false;
  }
};

export default apiClient;