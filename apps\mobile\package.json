{"name": "mobile", "version": "1.0.0", "private": true, "description": "HER移动应用 - React Native", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "eas build", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit": "eas submit", "test": "jest", "test:ci": "jest --ci --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "typecheck": "tsc --noEmit", "clean": "rm -rf node_modules .expo dist", "eject": "expo eject"}, "dependencies": {"@her/shared": "workspace:*", "@her/ui": "workspace:*", "@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "expo": "~50.0.0", "expo-application": "~5.8.3", "expo-auth-session": "~5.4.0", "expo-constants": "~15.4.5", "expo-crypto": "~12.8.0", "expo-device": "~5.9.3", "expo-font": "~11.10.2", "expo-haptics": "~12.8.1", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.9.0", "expo-splash-screen": "~0.26.4", "expo-status-bar": "~1.11.1", "expo-system-ui": "~2.9.3", "react": "18.2.0", "react-native": "0.73.2", "react-native-gesture-handler": "~2.14.0", "react-native-mmkv": "^2.11.0", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@babel/core": "^7.23.6", "@expo/config-plugins": "~7.8.4", "@react-native-community/eslint-config": "^3.2.0", "@types/jest": "^29.5.11", "@types/react": "~18.2.45", "@types/react-native": "~0.72.8", "@types/react-test-renderer": "^18.0.7", "jest": "^29.7.0", "jest-expo": "~50.0.1", "metro-config": "^0.80.4", "react-test-renderer": "18.2.0", "typescript": "^5.3.3"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}