/**
 * 统一错误处理中间件
 * 使用方法：withErrorHandler(handler)包装API函数以获得统一的错误处理
 */

import { VercelRequest, VercelResponse } from '@vercel/node';
import { randomUUID } from 'crypto';

export interface APIError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
  timestamp?: string;
  requestId?: string;
}

export interface APIErrorResponse {
  error: APIError;
}

/**
 * 标准化的API错误类
 */
export class StandardAPIError extends Error implements APIError {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly details?: any;

  constructor(
    message: string,
    code: string = 'INTERNAL_ERROR',
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = 'StandardAPIError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * 预定义的错误类型
 */
export const APIErrors = {
  // 认证相关
  AUTH_MISSING: (message = '缺少认证令牌') =>
    new StandardAPIError(message, 'AUTH_MISSING', 401),
  
  AUTH_INVALID: (message = '认证令牌无效') =>
    new StandardAPIError(message, 'AUTH_INVALID', 401),
  
  AUTH_EXPIRED: (message = '认证令牌已过期') =>
    new StandardAPIError(message, 'AUTH_EXPIRED', 401),

  // 权限相关
  FORBIDDEN: (message = '权限不足') =>
    new StandardAPIError(message, 'FORBIDDEN', 403),

  // 请求相关
  BAD_REQUEST: (message = '请求参数错误', details?: any) =>
    new StandardAPIError(message, 'BAD_REQUEST', 400, details),
  
  NOT_FOUND: (message = '请求的资源不存在') =>
    new StandardAPIError(message, 'NOT_FOUND', 404),

  // 速率限制
  RATE_LIMITED: (message = '请求过于频繁，请稍后再试') =>
    new StandardAPIError(message, 'RATE_LIMITED', 429),

  // 外部服务
  AI_SERVICE_ERROR: (message = 'AI服务暂时不可用', details?: any) =>
    new StandardAPIError(message, 'AI_SERVICE_ERROR', 502, details),
  
  DATABASE_ERROR: (message = '数据库连接错误', details?: any) =>
    new StandardAPIError(message, 'DATABASE_ERROR', 503, details),

  // 业务逻辑
  VALIDATION_ERROR: (message = '数据验证失败', details?: any) =>
    new StandardAPIError(message, 'VALIDATION_ERROR', 422, details),

  // 内部错误
  INTERNAL_ERROR: (message = '服务器内部错误', details?: any) =>
    new StandardAPIError(message, 'INTERNAL_ERROR', 500, details),
};

/**
 * 错误处理中间件包装器
 */
export function withErrorHandler<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>
) {
  return async (req: VercelRequest, res: VercelResponse, ...args: T): Promise<R | void> => {
    const requestId = generateRequestId();
    
    // 添加请求ID到请求对象
    (req as any).requestId = requestId;

    try {
      return await handler(req, res, ...args);
    } catch (error: any) {
      handleError(error, req, res, requestId);
    }
  };
}

/**
 * 处理错误并返回标准格式的响应
 */
function handleError(
  error: any,
  req: VercelRequest,
  res: VercelResponse,
  requestId: string
): void {
  // 记录错误日志
  logError(error, req, requestId);

  // 构建错误响应
  const errorResponse = buildErrorResponse(error, requestId);

  // 设置响应头
  res.setHeader('Content-Type', 'application/json');
  
  // 返回错误响应
  res.status(errorResponse.error.statusCode || 500).json(errorResponse);
}

/**
 * 构建标准化的错误响应
 */
function buildErrorResponse(error: any, requestId: string): APIErrorResponse {
  const timestamp = new Date().toISOString();

  // 如果是标准API错误
  if (error instanceof StandardAPIError) {
    return {
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
        statusCode: error.statusCode,
        timestamp,
        requestId,
      },
    };
  }

  // 处理Zod验证错误
  if (error.name === 'ZodError') {
    return {
      error: {
        code: 'VALIDATION_ERROR',
        message: '请求数据验证失败',
        details: error.errors?.map((e: any) => ({
          path: e.path.join('.'),
          message: e.message,
        })),
        statusCode: 422,
        timestamp,
        requestId,
      },
    };
  }

  // 处理JWT错误
  if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
    return {
      error: {
        code: 'AUTH_INVALID',
        message: '认证令牌无效或已过期',
        statusCode: 401,
        timestamp,
        requestId,
      },
    };
  }

  // 处理OpenAI API错误
  if (error.name === 'APIError' && error.status) {
    return {
      error: {
        code: 'AI_SERVICE_ERROR',
        message: 'AI服务错误',
        details: {
          status: error.status,
          message: error.message,
        },
        statusCode: 502,
        timestamp,
        requestId,
      },
    };
  }

  // 默认内部错误
  return {
    error: {
      code: 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'development' ? error.message : '服务器内部错误',
      details: process.env.NODE_ENV === 'development' ? { stack: error.stack } : undefined,
      statusCode: 500,
      timestamp,
      requestId,
    },
  };
}

/**
 * 记录错误日志
 */
function logError(error: any, req: VercelRequest, requestId: string): void {
  const logData = {
    requestId,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: {
        'user-agent': req.headers['user-agent'],
        'content-type': req.headers['content-type'],
      },
      body: req.method !== 'GET' ? req.body : undefined,
    },
    timestamp: new Date().toISOString(),
  };

  // 根据错误类型决定日志级别
  if (error instanceof StandardAPIError && error.statusCode < 500) {
    console.warn('API Warning:', JSON.stringify(logData, null, 2));
  } else {
    console.error('API Error:', JSON.stringify(logData, null, 2));
  }
}

/**
 * 生成唯一的请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${randomUUID().split('-')[0]}`;
}

/**
 * 组合多个中间件
 */
export function combineMiddlewares<T extends any[], R>(
  handler: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>,
  middlewares: Array<(handler: any) => any>
) {
  return middlewares.reduce((acc, middleware) => middleware(acc), handler);
}

/**
 * 异步异常捕获装饰器
 */
export function catchAsync<T extends any[], R>(
  fn: (req: VercelRequest, res: VercelResponse, ...args: T) => Promise<R>
) {
  return (req: VercelRequest, res: VercelResponse, ...args: T) => {
    Promise.resolve(fn(req, res, ...args)).catch((error) => {
      throw error; // 让errorHandler处理
    });
  };
}