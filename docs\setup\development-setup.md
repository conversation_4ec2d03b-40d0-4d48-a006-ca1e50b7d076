# HER 开发环境配置指南

## 📋 前置要求

- Node.js 18+ 
- pnpm 8+
- Git
- iOS 开发: Xcode 14+ (仅 Mac)
- Android 开发: Android Studio

## 🚀 快速开始

### 1. 克隆项目并安装依赖

```bash
# 安装依赖
pnpm install

# 构建共享包
pnpm shared:build
pnpm ui:build
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑 .env.local 填入实际值
```

### 3. 配置外部服务

#### 📊 Supabase 配置

1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 创建新项目 "HER-Production"
3. 获取配置信息：
   - Settings → API → 获取 `Project URL` 和 `anon public key`
   - Settings → API → 获取 `service_role key` (保密!)

4. 初始化数据库：

```sql
-- 创建用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  phone TEXT UNIQUE,
  nickname TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  preferences JSONB DEFAULT '{}'::jsonb
);

-- 创建对话表
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 创建消息表
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
  sender TEXT NOT NULL CHECK (sender IN ('user', 'her')),
  content TEXT NOT NULL,
  emotion TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建记忆表 (向量存储)
CREATE TABLE memories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  embedding vector(1536),
  importance FLOAT DEFAULT 0.5,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- 创建情绪记录表
CREATE TABLE mood_checkins (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  mood_value FLOAT NOT NULL CHECK (mood_value >= 0 AND mood_value <= 1),
  mood_label TEXT,
  note TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用 RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE memories ENABLE ROW LEVEL SECURITY;
ALTER TABLE mood_checkins ENABLE ROW LEVEL SECURITY;

-- 创建 RLS 策略
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own conversations" ON conversations
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view own messages" ON messages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage own memories" ON memories
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own mood checkins" ON mood_checkins
  FOR ALL USING (auth.uid() = user_id);

-- 创建索引
CREATE INDEX idx_messages_conversation ON messages(conversation_id);
CREATE INDEX idx_memories_user ON memories(user_id);
CREATE INDEX idx_mood_checkins_user_date ON mood_checkins(user_id, created_at DESC);
```

#### 🤖 OpenAI 配置

1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 创建新的 API Key
3. 设置使用限额和权限
4. 将 key 添加到 `.env.local`

#### 🚀 Vercel 配置 (部署时)

1. 安装 Vercel CLI：
```bash
npm i -g vercel
```

2. 登录并关联项目：
```bash
vercel login
vercel link
```

3. 配置环境变量：
```bash
vercel env add OPENAI_API_KEY
vercel env add SUPABASE_SERVICE_ROLE_KEY
# ... 添加其他必要的环境变量
```

### 4. 启动开发服务器

#### 启动所有服务
```bash
pnpm dev
```

#### 或分别启动

##### API 服务器 (Vercel Functions)
```bash
pnpm api:dev
# 访问 http://localhost:3000/api
```

##### 移动应用 (Expo)
```bash
pnpm mobile:start

# iOS 模拟器
pnpm mobile:ios

# Android 模拟器  
pnpm mobile:android
```

### 5. 验证配置

运行配置检查脚本：

```bash
pnpm test:env
```

应该看到：
```
✅ Supabase 连接成功
✅ OpenAI API 可用
✅ 数据库表已创建
✅ 环境变量配置完整
```

## 🧪 测试账户

开发环境提供测试账户：
- 手机号：13800138000
- 验证码：123456 (开发环境)

## 📱 移动端调试

### iOS 真机调试
1. 在 Xcode 中打开 `ios/` 目录
2. 选择你的开发团队
3. 连接设备并运行

### Android 真机调试
1. 开启开发者模式和 USB 调试
2. 运行 `adb devices` 确认连接
3. 运行 `pnpm mobile:android`

## 🐛 常见问题

### pnpm install 失败
```bash
# 清理缓存
pnpm store prune
rm -rf node_modules
pnpm install
```

### Supabase 连接失败
- 检查防火墙设置
- 确认 API key 正确
- 验证项目 URL

### OpenAI API 超时
- 使用代理或 VPN
- 配置 timeout 参数
- 检查 API 额度

### Expo 启动失败
```bash
# 清理缓存
npx expo start -c

# 重置 Metro
npx react-native start --reset-cache
```

## 📚 开发资源

- [Supabase 文档](https://supabase.com/docs)
- [OpenAI API 文档](https://platform.openai.com/docs)
- [React Native 文档](https://reactnative.dev)
- [Expo 文档](https://docs.expo.dev)
- [Vercel 文档](https://vercel.com/docs)

## 🔐 安全提醒

⚠️ **重要**：
- 永远不要提交 `.env.local` 到 Git
- 定期轮换 API keys
- 生产环境使用不同的密钥
- 启用 API 使用限额
- 实施速率限制

## 💡 开发技巧

1. **热重载**: 修改代码后自动刷新
2. **调试工具**: React Native Debugger / Flipper
3. **性能监控**: React DevTools Profiler
4. **网络调试**: 使用 Charles 或 Proxyman

---

准备好了吗？让我们开始创造温暖的陪伴体验！ 💝