{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "types": ["node"]}, "include": ["api/**/*", "lib/**/*", "middleware/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*", ".vercel"], "references": [{"path": "../shared"}]}