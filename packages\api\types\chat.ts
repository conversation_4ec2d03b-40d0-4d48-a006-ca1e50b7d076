/**
 * Chat API 类型定义
 * 定义聊天相关的TypeScript接口和类型
 */

import { z } from 'zod';
import type OpenAI from 'openai';
import type { 
  SendMessageRequest as SharedSendMessageRequest,
  SendMessageResponse as SharedSendMessageResponse 
} from '@her/shared';

// ============================================================================
// 基础类型
// ============================================================================

/**
 * 消息角色
 */
export type MessageRole = 'user' | 'assistant' | 'system';

/**
 * 聊天消息接口
 */
export interface ChatMessage {
  id: string;
  conversationId: string;
  role: MessageRole;
  content: string;
  createdAt: string;
  metadata?: {
    model?: string;
    tokens?: number;
    emotion?: string;
    temperature?: number;
  };
}

/**
 * 对话会话接口
 */
export interface Conversation {
  id: string;
  userId: string;
  title?: string;
  createdAt: string;
  updatedAt: string;
  lastMessageAt?: string;
  messageCount: number;
  metadata?: {
    totalTokens?: number;
    avgEmotionScore?: number;
    lastEmotion?: string;
  };
  isArchived: boolean;
}

/**
 * 上下文消息（用于AI API调用）
 */
export type ContextMessage = OpenAI.Chat.ChatCompletionMessageParam;

// ============================================================================
// API请求/响应类型
// ============================================================================

/**
 * 发送消息请求验证 - 基于shared包类型
 */

export const SendMessageRequestSchema = z.object({
  conversationId: z.string().uuid().optional(),
  message: z.string().min(1).max(4000),
  parentMessageId: z.string().uuid().optional(),
  metadata: z.object({
    timestamp: z.string().datetime().optional(),
    clientId: z.string().optional(),
    platform: z.enum(['mobile', 'web']).optional(),
  }).optional(),
});

// 使用shared包的类型定义，确保类型一致性
export type SendMessageRequest = SharedSendMessageRequest;

/**
 * 流式响应数据块
 */
export interface StreamChunk {
  type: 'content' | 'metadata' | 'done' | 'error';
  data?: {
    content?: string;
    messageId?: string;
    conversationId?: string;
    tokens?: number;
    model?: string;
    emotion?: string;
    error?: string;
  };
}

/**
 * 发送消息响应 - 基于shared包类型
 */
export type SendMessageResponse = SharedSendMessageResponse;

/**
 * 获取对话列表请求
 */
export const GetConversationsRequestSchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  archived: z.coerce.boolean().optional(), // 是否包含已归档
});

export type GetConversationsRequest = z.infer<typeof GetConversationsRequestSchema>;

/**
 * 获取对话列表响应
 */
export interface GetConversationsResponse {
  success: true;
  data: {
    conversations: Conversation[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

/**
 * 获取消息历史请求
 */
export const GetMessagesRequestSchema = z.object({
  conversationId: z.string().uuid(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(50),
  before: z.string().datetime().optional(), // 获取指定时间之前的消息
});

export type GetMessagesRequest = z.infer<typeof GetMessagesRequestSchema>;

/**
 * 获取消息历史响应
 */
export interface GetMessagesResponse {
  success: true;
  data: {
    messages: ChatMessage[];
    conversation: Conversation;
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
  };
}

// ============================================================================
// AI相关类型
// ============================================================================

/**
 * AI模型配置
 */
export interface ModelConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

/**
 * 系统提示词配置
 */
export interface SystemPrompt {
  role: 'system';
  content: string;
  metadata?: {
    version: string;
    personality: string;
    mood?: string;
  };
}

/**
 * 上下文配置
 */
export interface ContextConfig {
  maxMessages: number; // 最大消息数量
  maxTokens: number;   // 最大token数量
  includeSystem: boolean; // 是否包含系统提示词
  includeMemory: boolean; // 是否包含记忆上下文
}

// ============================================================================
// 情感分析类型
// ============================================================================

/**
 * 情感分析结果
 */
export interface EmotionAnalysis {
  primary: string;      // 主要情感
  confidence: number;   // 置信度 0-1
  intensity: number;    // 强度 0-1
  secondary?: string[];  // 次要情感
  valence: number;      // 情感倾向 -1到1
  arousal: number;      // 激活度 0-1
}

/**
 * 情感标签
 */
export const EMOTION_LABELS = {
  // 积极情感
  happy: '开心',
  excited: '兴奋', 
  grateful: '感激',
  confident: '自信',
  peaceful: '平静',
  loving: '爱意',
  
  // 消极情感
  sad: '难过',
  angry: '愤怒',
  anxious: '焦虑',
  frustrated: '沮丧',
  lonely: '孤独',
  confused: '困惑',
  
  // 中性情感
  neutral: '中性',
  thoughtful: '深思',
  curious: '好奇',
  tired: '疲惫',
} as const;

export type EmotionLabel = keyof typeof EMOTION_LABELS;

// ============================================================================
// 错误类型
// ============================================================================

/**
 * Chat API 错误代码
 */
export const CHAT_ERROR_CODES = {
  INVALID_MESSAGE: 'INVALID_MESSAGE',
  CONVERSATION_NOT_FOUND: 'CONVERSATION_NOT_FOUND',
  MESSAGE_TOO_LONG: 'MESSAGE_TOO_LONG',
  AI_SERVICE_UNAVAILABLE: 'AI_SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  CONTEXT_TOO_LONG: 'CONTEXT_TOO_LONG',
  INVALID_CONVERSATION_ID: 'INVALID_CONVERSATION_ID',
} as const;

export type ChatErrorCode = typeof CHAT_ERROR_CODES[keyof typeof CHAT_ERROR_CODES];

/**
 * Chat API 错误
 */
export interface ChatError {
  code: ChatErrorCode;
  message: string;
  details?: any;
}

// ============================================================================
// 工具类型
// ============================================================================

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number;
  limit: number;
  offset?: number;
}

/**
 * 分页响应
 */
export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasMore: boolean;
}

/**
 * 统一API响应格式
 */
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ChatError;
  requestId?: string;
  timestamp?: string;
}

/**
 * Server-Sent Events 事件类型
 */
export type SSEEventType = 
  | 'message-start'
  | 'message-delta'
  | 'message-stop'
  | 'error'
  | 'ping';

/**
 * SSE事件数据
 */
export interface SSEEvent {
  event?: SSEEventType;
  data: string;
  id?: string;
  retry?: number;
}

// ============================================================================
// 导出验证函数
// ============================================================================

/**
 * 验证消息内容
 */
export function validateMessage(content: string): boolean {
  return content.length > 0 && content.length <= 4000;
}

/**
 * 验证对话ID
 */
export function validateConversationId(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
}

/**
 * 创建SSE事件字符串
 */
export function formatSSEEvent(event: Partial<SSEEvent>): string {
  let formatted = '';
  
  if (event.event) {
    formatted += `event: ${event.event}\n`;
  }
  
  if (event.id) {
    formatted += `id: ${event.id}\n`;
  }
  
  if (event.retry) {
    formatted += `retry: ${event.retry}\n`;
  }
  
  formatted += `data: ${event.data}\n\n`;
  
  return formatted;
}