/**
 * HER Main Navigation
 * 主应用导航
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useTheme } from '@her/ui';
import { ChatScreen } from '../screens/main/ChatScreen';
import { MoodScreen } from '../screens/main/MoodScreen';
import { SettingsScreen } from '../screens/main/SettingsScreen';

export type MainTabParamList = {
  Chat: undefined;
  Mood: undefined;
  Settings: undefined;
};

const MainTab = createBottomTabNavigator<MainTabParamList>();

export const MainNavigator: React.FC = () => {
  const theme = useTheme();

  return (
    <MainTab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.border,
          paddingBottom: 20,
          paddingTop: 8,
          height: 80,
        },
        tabBarActiveTintColor: theme.colors.accent,
        tabBarInactiveTintColor: theme.colors.text.secondary,
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
        },
      }}
    >
      <MainTab.Screen
        name="Chat"
        component={ChatScreen}
        options={{
          tabBarLabel: '对话',
          // 这里可以添加图标
        }}
      />
      <MainTab.Screen
        name="Mood"
        component={MoodScreen}
        options={{
          tabBarLabel: '心情',
        }}
      />
      <MainTab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          tabBarLabel: '设置',
        }}
      />
    </MainTab.Navigator>
  );
};