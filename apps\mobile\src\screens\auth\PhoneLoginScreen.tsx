/**
 * HER Phone Login Screen
 * 手机号登录页面
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '@her/ui';
import { useAuthStore } from '../../stores/authStore';
import { AuthStackParamList } from '../../navigation/AuthNavigator';
import { validatePhone } from '@her/shared';

type PhoneLoginScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'PhoneLogin'>;

interface Props {
  navigation: PhoneLoginScreenNavigationProp;
}

export const PhoneLoginScreen: React.FC<Props> = ({ navigation }) => {
  const theme = useTheme();
  const { loginWithPhone } = useAuthStore();
  
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 格式化手机号显示
  const formatPhoneDisplay = (phoneNumber: string) => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length <= 3) return cleaned;
    if (cleaned.length <= 7) return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7, 11)}`;
  };

  const handlePhoneChange = (text: string) => {
    // 只保留数字
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length <= 11) {
      setPhone(cleaned);
    }
  };

  const handleSendCode = async () => {
    if (!isValidPhone(phone)) {
      Alert.alert('提示', '请输入正确的手机号');
      return;
    }

    setIsLoading(true);
    
    try {
      // 这里应该调用发送验证码的API
      // await authService.sendVerificationCode(phone);
      
      setIsCodeSent(true);
      startCountdown();
      Alert.alert('验证码已发送', `验证码已发送到 ${formatPhoneDisplay(phone)}`);
    } catch (error) {
      Alert.alert('发送失败', '发送验证码失败，请稍后重试');
      console.error('Send code failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async () => {
    if (!code || code.length !== 6) {
      Alert.alert('提示', '请输入6位验证码');
      return;
    }

    setIsLoading(true);
    
    try {
      await loginWithPhone(phone, code);
    } catch (error) {
      Alert.alert('登录失败', '验证码错误或已过期，请重新获取');
      console.error('Login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const isValidPhone = (phoneNumber: string) => {
    return /^1[3-9]\d{9}$/.test(phoneNumber);
  };

  const canSendCode = phone.length === 11 && isValidPhone(phone) && countdown === 0;
  const canLogin = isCodeSent && code.length === 6;

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Text style={[styles.backButtonText, { color: theme.colors.text.primary }]}>
            ← 返回
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text.primary }]}>
          手机号登录
        </Text>
        
        <Text style={[styles.subtitle, { color: theme.colors.text.secondary }]}>
          {isCodeSent ? '请输入验证码' : '请输入您的手机号'}
        </Text>

        {/* Phone Input */}
        <View style={styles.inputSection}>
          <View style={[styles.inputContainer, { borderColor: theme.colors.border }]}>
            <Text style={[styles.prefix, { color: theme.colors.text.secondary }]}>
              +86
            </Text>
            <TextInput
              style={[styles.input, { color: theme.colors.text.primary }]}
              placeholder="请输入手机号"
              placeholderTextColor={theme.colors.text.secondary}
              value={formatPhoneDisplay(phone)}
              onChangeText={handlePhoneChange}
              keyboardType="number-pad"
              maxLength={13} // 包含空格
              editable={!isCodeSent}
            />
          </View>

          {!isCodeSent && (
            <TouchableOpacity
              style={[
                styles.sendButton,
                {
                  backgroundColor: canSendCode ? theme.colors.accent : theme.colors.border,
                },
              ]}
              onPress={handleSendCode}
              disabled={!canSendCode || isLoading}
            >
              <Text
                style={[
                  styles.sendButtonText,
                  { 
                    color: canSendCode ? theme.colors.text.primary : theme.colors.text.secondary,
                  },
                ]}
              >
                {isLoading ? '发送中...' : '发送验证码'}
              </Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Code Input */}
        {isCodeSent && (
          <View style={styles.inputSection}>
            <View style={[styles.inputContainer, { borderColor: theme.colors.border }]}>
              <TextInput
                style={[styles.input, { color: theme.colors.text.primary }]}
                placeholder="请输入6位验证码"
                placeholderTextColor={theme.colors.text.secondary}
                value={code}
                onChangeText={setCode}
                keyboardType="number-pad"
                maxLength={6}
              />
            </View>

            <TouchableOpacity
              style={[
                styles.resendButton,
                { opacity: countdown > 0 ? 0.5 : 1 },
              ]}
              onPress={handleSendCode}
              disabled={countdown > 0 || isLoading}
            >
              <Text style={[styles.resendButtonText, { color: theme.colors.accent }]}>
                {countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Login Button */}
        {isCodeSent && (
          <TouchableOpacity
            style={[
              styles.loginButton,
              {
                backgroundColor: canLogin ? theme.colors.accent : theme.colors.border,
              },
            ]}
            onPress={handleLogin}
            disabled={!canLogin || isLoading}
          >
            <Text
              style={[
                styles.loginButtonText,
                { 
                  color: canLogin ? theme.colors.text.primary : theme.colors.text.secondary,
                },
              ]}
            >
              {isLoading ? '登录中...' : '登录'}
            </Text>
          </TouchableOpacity>
        )}

        <Text style={[styles.disclaimer, { color: theme.colors.text.secondary }]}>
          登录即表示同意服务条款和隐私政策
        </Text>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  backButton: {
    alignSelf: 'flex-start',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 32,
  },
  inputSection: {
    marginBottom: 24,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
    marginBottom: 12,
  },
  prefix: {
    fontSize: 16,
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  sendButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  resendButton: {
    alignSelf: 'flex-end',
    paddingVertical: 8,
  },
  resendButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loginButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 12,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  disclaimer: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
    marginTop: 24,
  },
});