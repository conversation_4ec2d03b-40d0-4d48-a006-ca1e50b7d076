/**
 * HER Root Navigation
 * 应用主导航器
 */

import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useTheme } from '@her/ui';

import { useAuthStore } from '../stores/authStore';
import { useAppStore } from '../stores/appStore';
import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';
import { OnboardingScreen } from '../screens/OnboardingScreen';
import { SplashScreen } from '../screens/SplashScreen';

export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
};

const RootStack = createNativeStackNavigator<RootStackParamList>();

export const RootNavigator: React.FC = () => {
  const theme = useTheme();
  const { isAuthenticated, isLoading: authLoading } = useAuthStore();
  const { isInitialized, isFirstLaunch } = useAppStore();

  // 应用未初始化时显示启动画面
  if (!isInitialized || authLoading) {
    return (
      <NavigationContainer
        theme={{
          dark: theme.isDark,
          colors: {
            primary: theme.colors.accent,
            background: theme.colors.background,
            card: theme.colors.surface,
            text: theme.colors.text.primary,
            border: theme.colors.border,
            notification: theme.colors.accent,
          },
        }}
      >
        <RootStack.Navigator
          screenOptions={{
            headerShown: false,
            animation: 'fade',
          }}
        >
          <RootStack.Screen name="Splash" component={SplashScreen} />
        </RootStack.Navigator>
      </NavigationContainer>
    );
  }

  return (
    <NavigationContainer
      theme={{
        dark: theme.isDark,
        colors: {
          primary: theme.colors.accent,
          background: theme.colors.background,
          card: theme.colors.surface,
          text: theme.colors.text.primary,
          border: theme.colors.border,
          notification: theme.colors.accent,
        },
      }}
    >
      <RootStack.Navigator
        screenOptions={{
          headerShown: false,
          animation: 'slide_from_right',
        }}
        initialRouteName={
          isFirstLaunch 
            ? 'Onboarding' 
            : isAuthenticated 
              ? 'Main' 
              : 'Auth'
        }
      >
        {isFirstLaunch && (
          <RootStack.Screen 
            name="Onboarding" 
            component={OnboardingScreen}
            options={{ animation: 'fade' }}
          />
        )}
        
        {!isAuthenticated ? (
          <RootStack.Screen 
            name="Auth" 
            component={AuthNavigator}
            options={{ animation: 'fade' }}
          />
        ) : (
          <RootStack.Screen 
            name="Main" 
            component={MainNavigator}
            options={{ animation: 'fade' }}
          />
        )}
      </RootStack.Navigator>
    </NavigationContainer>
  );
};