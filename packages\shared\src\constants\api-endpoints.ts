/**
 * HER API 端点常量
 * 统一管理所有API路径
 */

// 基础路径
export const API_BASE = '/api';

// 认证端点
export const AUTH_ENDPOINTS = {
  ANONYMOUS: `${API_BASE}/auth/anonymous`,
  PHONE: `${API_BASE}/auth/phone`,
  REFRESH: `${API_BASE}/auth/refresh`,
  LOGOUT: `${API_BASE}/auth/logout`,
  VERIFY: `${API_BASE}/auth/verify`,
} as const;

// 用户端点
export const USER_ENDPOINTS = {
  PROFILE: `${API_BASE}/user/profile`,
  PREFERENCES: `${API_BASE}/user/preferences`,
  DELETE: `${API_BASE}/user/delete`,
} as const;

// 对话端点
export const CHAT_ENDPOINTS = {
  SEND: `${API_BASE}/chat/send`,
  STREAM: `${API_BASE}/chat/stream`,
  CONVERSATIONS: `${API_BASE}/chat/conversations`,
  MESSAGES: (conversationId: string) => `${API_BASE}/chat/messages/${conversationId}`,
  DELETE_CONVERSATION: (conversationId: string) => `${API_BASE}/chat/conversations/${conversationId}`,
  ARCHIVE_CONVERSATION: (conversationId: string) => `${API_BASE}/chat/conversations/${conversationId}/archive`,
} as const;

// 记忆端点
export const MEMORY_ENDPOINTS = {
  SEARCH: `${API_BASE}/memory/search`,
  SAVE: `${API_BASE}/memory/save`,
  DELETE: (memoryId: string) => `${API_BASE}/memory/${memoryId}`,
  LIST: `${API_BASE}/memory/list`,
  CLEANUP: `${API_BASE}/memory/cleanup`,
} as const;

// 情绪端点
export const MOOD_ENDPOINTS = {
  CHECKIN: `${API_BASE}/mood/checkin`,
  HISTORY: `${API_BASE}/mood/history`,
  STATS: `${API_BASE}/mood/stats`,
  DELETE: (checkinId: string) => `${API_BASE}/mood/${checkinId}`,
} as const;

// 系统端点
export const SYSTEM_ENDPOINTS = {
  HEALTH: `${API_BASE}/health`,
  VERSION: `${API_BASE}/version`,
  CONFIG: `${API_BASE}/config`,
} as const;

// WebSocket端点
export const WS_ENDPOINTS = {
  CHAT: '/ws/chat',
  NOTIFICATIONS: '/ws/notifications',
} as const;

// 文件上传端点
export const UPLOAD_ENDPOINTS = {
  AVATAR: `${API_BASE}/upload/avatar`,
  VOICE: `${API_BASE}/upload/voice`,
  IMAGE: `${API_BASE}/upload/image`,
} as const;

// 分析端点
export const ANALYTICS_ENDPOINTS = {
  EVENT: `${API_BASE}/analytics/event`,
  BATCH: `${API_BASE}/analytics/batch`,
} as const;

// 端点工具函数
export const buildUrl = (endpoint: string, params?: Record<string, string | number>) => {
  if (!params) return endpoint;
  
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    searchParams.append(key, String(value));
  });
  
  return `${endpoint}?${searchParams.toString()}`;
};

// 获取完整URL
export const getFullUrl = (endpoint: string, baseUrl?: string) => {
  if (!baseUrl) return endpoint;
  return `${baseUrl.replace(/\/$/, '')}${endpoint}`;
};