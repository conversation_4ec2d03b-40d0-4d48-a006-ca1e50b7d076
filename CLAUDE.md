# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

HER是一个基于React Native + Vercel + Supabase架构的私密AI陪伴应用，采用monorepo结构和TypeScript全栈开发。项目专注于提供温暖、私密、智能的AI陪伴体验，具备端到端加密、情感计算和隐私优先的特色。

## 项目架构

### Monorepo结构
- 使用 `pnpm workspaces` 管理依赖
- TypeScript项目引用 (tsconfig references) 保证类型安全
- 共享包统一管理公共代码

### 主要目录结构
```
apps/mobile/          # React Native移动应用 (Expo)
packages/api/         # Vercel Edge Functions (后端API)
packages/shared/      # 共享类型定义和工具函数
packages/ui/          # 共享UI组件库
docs/                # 技术文档和设计文档
```

## 开发命令

### 通用命令
```bash
# 安装依赖并构建共享包
pnpm install

# 启动所有开发服务
pnpm dev

# 运行所有测试
pnpm test

# 类型检查
pnpm typecheck

# 代码检查和格式化
pnpm lint
pnpm lint:fix
pnpm format
```

### 移动端开发
```bash
# 启动移动端开发服务器
pnpm mobile:dev

# 在iOS模拟器中运行 (仅macOS)
pnpm mobile:ios

# 在Android模拟器中运行
pnpm mobile:android

# EAS构建
pnpm mobile:build
```

### API开发
```bash
# 启动本地API开发服务器 (Vercel Dev)
pnpm api:dev

# 部署到Vercel生产环境
pnpm api:deploy

# 部署到Vercel预览环境
pnpm api:deploy:preview
```

### 共享包开发
```bash
# 构建shared包 (必须在移动端开发前执行)
pnpm shared:build

# 构建UI组件包
pnpm ui:build

# 监听模式下构建shared包
pnpm --filter shared dev
```

## 技术栈

### 前端
- **框架**: React Native 0.73 + Expo SDK 50
- **状态管理**: Zustand (轻量级)
- **导航**: React Navigation 6.x
- **动画**: React Native Reanimated 3.x
- **本地存储**: MMKV (高性能), Expo Secure Store (敏感数据)

### 后端
- **运行时**: Vercel Edge Functions (Node.js 20)
- **数据库**: Supabase (PostgreSQL + pgvector)
- **缓存**: Vercel KV (Redis)
- **文件存储**: Supabase Storage
- **认证**: Supabase Auth (支持匿名登录)

### AI集成
- **对话**: OpenAI GPT-4 Turbo (流式响应)
- **向量搜索**: OpenAI text-embedding-3 + pgvector
- **情感分析**: 基于GPT-4的上下文理解

## 关键设计原则

### 隐私优先
- 所有敏感数据本地加密存储
- 消息内容端到端加密
- 支持匿名登录，无需手机号
- 遵循GDPR和数据保护法规

### 情感响应
- 低延迟流式AI响应 (< 2秒首字节)
- 上下文记忆系统 (向量搜索)
- 实时情绪识别和适应
- 个性化对话生成

### 性能优化
- 边缘计算部署 (全球CDN)
- 智能缓存策略 (本地+Redis)
- 懒加载和代码分割
- 原生动画性能

## 开发工作流

### 依赖关系
1. `packages/shared` 必须先构建 (`pnpm shared:build`)
2. `packages/ui` 依赖 shared，需要先构建 (`pnpm ui:build`)
3. `apps/mobile` 依赖 shared 和 ui
4. `packages/api` 依赖 shared

### 新功能开发流程
1. **类型定义**: 在 `packages/shared/src/types/` 中定义接口
2. **API实现**: 在 `packages/api/functions/` 中创建端点
3. **UI组件**: 在 `packages/ui/src/components/` 中创建可复用组件
4. **移动端集成**: 在 `apps/mobile/src/` 中使用组件和API

### Git工作流
- 功能分支: `feature/功能名称`
- 提交规范: Conventional Commits (feat:, fix:, docs:, etc.)
- 提交前检查: husky + lint-staged 自动运行测试和格式化

## 环境配置

### 必需的环境变量
```bash
# Frontend (.env.local)
EXPO_PUBLIC_API_URL=https://api.her-app.com
EXPO_PUBLIC_SUPABASE_URL=https://xxx.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=xxx

# Backend (Vercel Environment Variables)
OPENAI_API_KEY=sk-xxx
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_KEY=xxx
JWT_SECRET=xxx
KV_REST_API_URL=xxx
KV_REST_API_TOKEN=xxx
```

## 测试策略

### 单元测试
- React Native组件: `@testing-library/react-native`
- API函数: Jest + `node-mocks-http`
- 共享工具: Jest

### 集成测试
- API端点测试 (模拟数据库)
- 组件集成测试
- 状态管理测试

### E2E测试
- Detox (React Native端到端测试)
- 关键用户流程覆盖

## 部署

### 生产环境
- **移动端**: Expo EAS Build → App Store/Google Play
- **API**: Vercel (自动部署 main 分支)
- **数据库**: Supabase Managed PostgreSQL
- **CDN**: Cloudflare (全球加速)

### 环境
- Development: http://localhost:3000/api
- Staging: https://staging-api.her-app.com
- Production: https://api.her-app.com

## 故障排除

### 常见问题
```bash
# Metro bundler缓存问题
pnpm mobile:clean && pnpm install && pnpm shared:build && pnpm ui:build

# TypeScript类型错误
pnpm shared:build  # 确保shared包已构建

# iOS构建失败
cd apps/mobile/ios && pod install

# Vercel部署问题
vercel env ls  # 检查环境变量
vercel logs    # 查看部署日志
```

### 性能优化
- 使用 `pnpm` 代替 `npm` (更快的包管理)
- 构建缓存: `.vercel/cache`, `node_modules/.cache`
- 并行构建: `pnpm -r` 命令会并行执行

## 代码规范

### TypeScript规范
- 严格类型检查 (strict: true)
- 共享类型定义统一管理
- 避免 `any` 类型，使用 `unknown` 或具体类型

### React Native规范  
- 函数组件 + Hooks
- memo包装性能敏感组件
- 使用 NativeWind 进行样式管理

### API规范
- RESTful设计，支持流式响应
- 统一错误处理和响应格式
- 请求限流和身份验证中间件

## 文档资源

- [架构文档](docs/architecture.md) - 详细的技术架构设计
- [产品需求文档](docs/prd.md) - 产品功能和需求说明
- [设计系统](docs/design/design-system.md) - UI/UX设计规范
- [开发计划](docs/development-plan.md) - 开发路线图和里程碑

## 特殊注意事项

### 安全考虑
- 敏感数据加密: 使用 `packages/shared/src/utils/encryption.ts`
- 不要在代码中硬编码API密钥或敏感信息
- 所有用户输入需要验证和清理

### AI集成最佳实践
- 使用流式响应提升用户体验
- 实现重试机制处理OpenAI API限流
- 缓存常见对话模板减少API调用
- 向量搜索结果缓存 (Redis TTL: 5分钟)

### 数据库操作
- 使用Supabase Row Level Security (RLS)
- 向量搜索优化: 定期更新索引
- 批量操作使用事务
- 定期清理过期数据和缓存