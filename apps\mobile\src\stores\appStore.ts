/**
 * HER App全局状态管理
 * 使用Zustand管理应用级别的状态
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ThemeVariant, ThemeMode, STORAGE_KEYS } from '@her/shared';

interface AppState {
  // 应用初始化状态
  isInitialized: boolean;
  isFirstLaunch: boolean;
  
  // 主题状态
  theme: {
    variant: ThemeVariant;
    mode: ThemeMode;
    isDark: boolean;
    isAutoTime: boolean; // 是否启用时间自动切换主题
  };
  
  // 应用设置
  settings: {
    notificationsEnabled: boolean;
    soundEnabled: boolean;
    hapticsEnabled: boolean;
    analyticsEnabled: boolean;
  };
  
  // 操作方法
  initializeApp: () => Promise<void>;
  setTheme: (variant: ThemeVariant, mode?: ThemeMode) => void;
  toggleThemeMode: () => void;
  enableAutoTimeTheme: (enabled: boolean) => void;
  updateSettings: (settings: Partial<AppState['settings']>) => void;
  resetApp: () => void;
}

// 获取时间自动主题
const getTimeBasedVariant = (): ThemeVariant => {
  const hour = new Date().getHours();
  
  if (hour >= 6 && hour < 12) {
    return 'default'; // 早晨
  } else if (hour >= 12 && hour < 18) {
    return 'peaceful'; // 下午
  } else if (hour >= 18 && hour < 22) {
    return 'warm'; // 傍晚
  } else {
    return 'night'; // 深夜
  }
};

// 检测系统暗色模式
const getSystemThemeMode = (): ThemeMode => {
  // 在React Native中，这里可能需要使用Appearance API
  // import { Appearance } from 'react-native';
  // return Appearance.getColorScheme() === 'dark' ? 'dark' : 'light';
  return 'light'; // 默认值
};

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      isInitialized: false,
      isFirstLaunch: true,
      
      theme: {
        variant: 'default',
        mode: 'auto',
        isDark: false,
        isAutoTime: true,
      },
      
      settings: {
        notificationsEnabled: true,
        soundEnabled: true,
        hapticsEnabled: true,
        analyticsEnabled: true,
      },
      
      // 初始化应用
      initializeApp: async () => {
        const state = get();
        
        try {
          // 检查是否首次启动
          const onboardingCompleted = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
          const isFirstLaunch = !onboardingCompleted;
          
          // 应用时间自动主题
          let currentVariant = state.theme.variant;
          if (state.theme.isAutoTime) {
            currentVariant = getTimeBasedVariant();
          }
          
          // 确定暗色模式状态
          const systemMode = getSystemThemeMode();
          const isDark = state.theme.mode === 'dark' || 
                         (state.theme.mode === 'auto' && systemMode === 'dark') ||
                         currentVariant === 'night';
          
          set({
            isInitialized: true,
            isFirstLaunch,
            theme: {
              ...state.theme,
              variant: currentVariant,
              isDark,
            },
          });
        } catch (error) {
          console.error('App initialization failed:', error);
          set({ isInitialized: true });
        }
      },
      
      // 设置主题
      setTheme: (variant: ThemeVariant, mode?: ThemeMode) => {
        const state = get();
        const newMode = mode || state.theme.mode;
        const systemMode = getSystemThemeMode();
        
        const isDark = newMode === 'dark' || 
                      (newMode === 'auto' && systemMode === 'dark') ||
                      variant === 'night';
        
        set({
          theme: {
            ...state.theme,
            variant,
            mode: newMode,
            isDark,
            isAutoTime: false, // 手动设置主题时禁用自动时间主题
          },
        });
      },
      
      // 切换主题模式
      toggleThemeMode: () => {
        const state = get();
        let newMode: ThemeMode;
        
        switch (state.theme.mode) {
          case 'light':
            newMode = 'dark';
            break;
          case 'dark':
            newMode = 'auto';
            break;
          default:
            newMode = 'light';
        }
        
        get().setTheme(state.theme.variant, newMode);
      },
      
      // 启用/禁用时间自动主题
      enableAutoTimeTheme: (enabled: boolean) => {
        const state = get();
        
        let variant = state.theme.variant;
        if (enabled) {
          variant = getTimeBasedVariant();
        }
        
        set({
          theme: {
            ...state.theme,
            variant,
            isAutoTime: enabled,
          },
        });
      },
      
      // 更新设置
      updateSettings: (newSettings: Partial<AppState['settings']>) => {
        const state = get();
        set({
          settings: {
            ...state.settings,
            ...newSettings,
          },
        });
      },
      
      // 重置应用
      resetApp: () => {
        set({
          isInitialized: false,
          isFirstLaunch: true,
          theme: {
            variant: 'default',
            mode: 'auto',
            isDark: false,
            isAutoTime: true,
          },
          settings: {
            notificationsEnabled: true,
            soundEnabled: true,
            hapticsEnabled: true,
            analyticsEnabled: true,
          },
        });
      },
    }),
    {
      name: 'her-app-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        // 只持久化这些字段
        isFirstLaunch: state.isFirstLaunch,
        theme: state.theme,
        settings: state.settings,
      }),
    }
  )
);