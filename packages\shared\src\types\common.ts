/**
 * HER 通用类型定义
 */

// 环境类型
export type Environment = 'development' | 'staging' | 'production';

// 平台类型
export type Platform = 'ios' | 'android' | 'web';

// 语言代码
export type LanguageCode = 'zh-CN' | 'zh-TW' | 'en' | 'ja' | 'ko';

// 时区类型
export type TimeZone = string;

// 设备信息
export interface DeviceInfo {
  platform: Platform;
  version: string;
  model: string;
  screenWidth: number;
  screenHeight: number;
  isTablet: boolean;
  hasNotch: boolean;
}

// 应用配置
export interface AppConfig {
  apiUrl: string;
  supabaseUrl: string;
  environment: Environment;
  version: string;
  buildNumber: string;
  features: FeatureFlags;
}

// 功能开关
export interface FeatureFlags {
  voiceChat: boolean;
  memorySearch: boolean;
  moodTracking: boolean;
  notifications: boolean;
  analytics: boolean;
  debugMode: boolean;
}

// 存储键名常量
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  CONVERSATIONS: 'conversations_cache',
  THEME: 'selected_theme',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  LAST_SYNC: 'last_sync_time',
} as const;

// 事件类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: Date;
  userId?: string;
  sessionId?: string;
}

// 错误类型
export class HerError extends Error {
  public readonly code: string;
  public readonly statusCode?: number;
  public readonly details?: any;

  constructor(
    message: string,
    code: string,
    statusCode?: number,
    details?: any
  ) {
    super(message);
    this.name = 'HerError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

// 通用操作结果
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: HerError;
}

// 缓存配置
export interface CacheConfig {
  ttl: number; // 生存时间（毫秒）
  maxSize: number; // 最大缓存条目数
  strategy: 'lru' | 'fifo' | 'ttl';
}