/**
 * HER 存储工具函数
 * 提供统一的数据存储和缓存功能
 */

import { STORAGE_KEYS, CacheConfig } from '../types/common';

// 存储接口定义
interface StorageInterface {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
}

// 内存缓存类
class MemoryCache {
  private cache = new Map<string, { value: any; expires: number }>();
  
  set(key: string, value: any, ttl: number = 300000): void { // 默认5分钟
    const expires = Date.now() + ttl;
    this.cache.set(key, { value, expires });
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
  
  delete(key: string): void {
    this.cache.delete(key);
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  // 清理过期项
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局内存缓存实例
const memoryCache = new MemoryCache();

// 定期清理过期缓存
setInterval(() => {
  memoryCache.cleanup();
}, 60000); // 每分钟清理一次

// 存储工具类
export class StorageUtils {
  private storage: StorageInterface;
  
  constructor(storage: StorageInterface) {
    this.storage = storage;
  }
  
  // 存储JSON数据
  async setJson<T>(key: string, value: T): Promise<void> {
    try {
      const jsonString = JSON.stringify(value);
      await this.storage.setItem(key, jsonString);
    } catch (error) {
      console.error(`Failed to store JSON for key ${key}:`, error);
      throw new Error('存储数据失败');
    }
  }
  
  // 获取JSON数据
  async getJson<T>(key: string): Promise<T | null> {
    try {
      const jsonString = await this.storage.getItem(key);
      if (!jsonString) return null;
      
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`Failed to parse JSON for key ${key}:`, error);
      return null;
    }
  }
  
  // 存储字符串数据
  async setString(key: string, value: string): Promise<void> {
    try {
      await this.storage.setItem(key, value);
    } catch (error) {
      console.error(`Failed to store string for key ${key}:`, error);
      throw new Error('存储数据失败');
    }
  }
  
  // 获取字符串数据
  async getString(key: string): Promise<string | null> {
    try {
      return await this.storage.getItem(key);
    } catch (error) {
      console.error(`Failed to get string for key ${key}:`, error);
      return null;
    }
  }
  
  // 存储布尔值
  async setBoolean(key: string, value: boolean): Promise<void> {
    await this.setString(key, value.toString());
  }
  
  // 获取布尔值
  async getBoolean(key: string): Promise<boolean | null> {
    const value = await this.getString(key);
    if (value === null) return null;
    return value === 'true';
  }
  
  // 存储数字
  async setNumber(key: string, value: number): Promise<void> {
    await this.setString(key, value.toString());
  }
  
  // 获取数字
  async getNumber(key: string): Promise<number | null> {
    const value = await this.getString(key);
    if (value === null) return null;
    
    const num = Number(value);
    return isNaN(num) ? null : num;
  }
  
  // 删除数据
  async remove(key: string): Promise<void> {
    try {
      await this.storage.removeItem(key);
    } catch (error) {
      console.error(`Failed to remove key ${key}:`, error);
    }
  }
  
  // 清空所有数据
  async clear(): Promise<void> {
    try {
      await this.storage.clear();
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  }
  
  // 批量操作
  async setMultiple(items: Record<string, any>): Promise<void> {
    const promises = Object.entries(items).map(([key, value]) => 
      this.setJson(key, value)
    );
    
    await Promise.all(promises);
  }
  
  async getMultiple<T>(keys: string[]): Promise<Record<string, T | null>> {
    const promises = keys.map(async (key) => {
      const value = await this.getJson<T>(key);
      return [key, value] as const;
    });
    
    const results = await Promise.all(promises);
    return Object.fromEntries(results);
  }
  
  async removeMultiple(keys: string[]): Promise<void> {
    const promises = keys.map(key => this.remove(key));
    await Promise.all(promises);
  }
}

// 缓存工具函数
export const CacheUtils = {
  // 设置缓存
  set: (key: string, value: any, ttl?: number): void => {
    memoryCache.set(key, value, ttl);
  },
  
  // 获取缓存
  get: <T>(key: string): T | null => {
    return memoryCache.get(key);
  },
  
  // 删除缓存
  delete: (key: string): void => {
    memoryCache.delete(key);
  },
  
  // 清空缓存
  clear: (): void => {
    memoryCache.clear();
  },
  
  // 带缓存的数据获取
  async getWithCache<T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl: number = 300000
  ): Promise<T> {
    // 先尝试从缓存获取
    let cached = memoryCache.get(key);
    if (cached !== null) {
      return cached;
    }
    
    // 缓存未命中，获取数据并缓存
    const data = await fetchFn();
    memoryCache.set(key, data, ttl);
    return data;
  },
};

// 预定义的存储键助手
export const StorageKeys = {
  // 用户相关
  userProfile: (userId: string) => `${STORAGE_KEYS.USER_PREFERENCES}_${userId}`,
  userSettings: (userId: string) => `user_settings_${userId}`,
  
  // 对话相关
  conversationCache: (conversationId: string) => `conversation_${conversationId}`,
  messageCache: (conversationId: string) => `messages_${conversationId}`,
  
  // 主题相关
  themePreference: STORAGE_KEYS.THEME,
  
  // 应用状态
  onboardingStatus: STORAGE_KEYS.ONBOARDING_COMPLETED,
  lastSyncTime: STORAGE_KEYS.LAST_SYNC,
  
  // 缓存键
  apiCache: (endpoint: string) => `api_cache_${endpoint}`,
  imageCache: (url: string) => `image_cache_${btoa(url).slice(0, 16)}`,
};

// 数据验证和清理
export const DataUtils = {
  // 验证存储的数据结构
  validateStoredData: <T>(data: any, validator: (data: any) => data is T): T | null => {
    try {
      if (validator(data)) {
        return data;
      }
    } catch (error) {
      console.warn('Data validation failed:', error);
    }
    return null;
  },
  
  // 清理过期数据
  cleanupExpiredData: async (storage: StorageUtils, keys: string[]): Promise<void> => {
    const promises = keys.map(async (key) => {
      try {
        const data = await storage.getJson<{ timestamp: number; data: any }>(key);
        if (data && data.timestamp && Date.now() - data.timestamp > 86400000) { // 24小时
          await storage.remove(key);
        }
      } catch (error) {
        console.warn(`Failed to cleanup key ${key}:`, error);
      }
    });
    
    await Promise.all(promises);
  },
  
  // 数据迁移助手
  migrateData: async (
    storage: StorageUtils,
    oldKey: string,
    newKey: string,
    transformer?: (data: any) => any
  ): Promise<void> => {
    try {
      const oldData = await storage.getJson(oldKey);
      if (oldData) {
        const newData = transformer ? transformer(oldData) : oldData;
        await storage.setJson(newKey, newData);
        await storage.remove(oldKey);
      }
    } catch (error) {
      console.error(`Data migration failed from ${oldKey} to ${newKey}:`, error);
    }
  },
};