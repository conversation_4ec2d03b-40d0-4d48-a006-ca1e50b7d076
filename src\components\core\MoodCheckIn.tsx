/**
 * MoodCheckIn Component
 * 情绪签到组件 - 温柔地了解用户的状态
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  PanResponder,
  Vibration,
  Dimensions,
} from 'react-native';
import { defaultTheme, emotionalColors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing, borderRadius, shadows } from '../../theme/spacing';

const { width: screenWidth } = Dimensions.get('window');

interface Mood {
  value: number; // 0-100
  label: string;
  color: string;
  emoji: string;
  description: string;
}

interface MoodCheckInProps {
  onMoodSelect?: (mood: Mood) => void;
  onSkip?: () => void;
  showSkip?: boolean;
  initialPrompt?: string;
  isMinimal?: boolean; // 极简模式
}

const MoodCheckIn: React.FC<MoodCheckInProps> = ({
  onMoodSelect,
  onSkip,
  showSkip = true,
  initialPrompt = '今天感觉怎么样？',
  isMinimal = false,
}) => {
  const [currentMood, setCurrentMood] = useState<number>(50);
  const [isInteracting, setIsInteracting] = useState(false);
  const slideAnim = useRef(new Animated.Value(50)).current;
  const colorAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // 情绪映射
  const getMoodFromValue = (value: number): Mood => {
    if (value <= 20) {
      return {
        value,
        label: '很低落',
        color: emotionalColors.caring.bubble,
        emoji: '😔',
        description: '我在这里陪你',
      };
    } else if (value <= 40) {
      return {
        value,
        label: '有点累',
        color: emotionalColors.listening.bubble,
        emoji: '😮‍💨',
        description: '慢慢来，不着急',
      };
    } else if (value <= 60) {
      return {
        value,
        label: '还好',
        color: defaultTheme.bubble.her,
        emoji: '😐',
        description: '平常的一天',
      };
    } else if (value <= 80) {
      return {
        value,
        label: '不错',
        color: emotionalColors.understanding.bubble,
        emoji: '😊',
        description: '看起来状态不错',
      };
    } else {
      return {
        value,
        label: '很棒',
        color: '#FFE5D4',
        emoji: '😄',
        description: '真为你开心',
      };
    }
  };

  // 创建滑动手势响应器
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      
      onPanResponderGrant: () => {
        setIsInteracting(true);
        // 开始交互动画
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }).start();
        
        // 轻微震动反馈
        Vibration.vibrate(10);
      },
      
      onPanResponderMove: (evt, gestureState) => {
        // 计算滑动位置对应的情绪值
        const sliderWidth = screenWidth - spacing.xl * 2;
        const locationX = Math.max(0, Math.min(gestureState.moveX - spacing.xl, sliderWidth));
        const moodValue = (locationX / sliderWidth) * 100;
        
        setCurrentMood(moodValue);
        slideAnim.setValue(moodValue);
        
        // 更新颜色动画
        Animated.timing(colorAnim, {
          toValue: moodValue / 100,
          duration: 100,
          useNativeDriver: false,
        }).start();
      },
      
      onPanResponderRelease: () => {
        setIsInteracting(false);
        // 恢复缩放
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }).start();
        
        // 震动确认
        Vibration.vibrate(20);
      },
    })
  ).current;

  // 入场动画
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 50,
        friction: 8,
        tension: 40,
        useNativeDriver: false,
      }),
    ]).start();
  }, []);

  const mood = getMoodFromValue(currentMood);

  // 渲染极简模式
  const renderMinimalMode = () => (
    <View style={styles.minimalContainer}>
      <TouchableOpacity
        style={[styles.minimalMood, { backgroundColor: mood.color }]}
        onPress={() => onMoodSelect?.(mood)}
      >
        <Text style={styles.minimalEmoji}>{mood.emoji}</Text>
      </TouchableOpacity>
      {showSkip && (
        <TouchableOpacity onPress={onSkip} style={styles.minimalSkip}>
          <Text style={styles.skipText}>跳过</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  // 渲染完整模式
  const renderFullMode = () => (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [20, 0],
            }),
          }],
        },
      ]}
    >
      {/* 问候语 */}
      <Text style={styles.prompt}>{initialPrompt}</Text>
      
      {/* 情绪emoji和标签 */}
      <View style={styles.moodDisplay}>
        <Animated.Text 
          style={[
            styles.moodEmoji,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {mood.emoji}
        </Animated.Text>
        <Text style={[styles.moodLabel, { color: mood.color }]}>
          {mood.label}
        </Text>
        <Text style={styles.moodDescription}>
          {mood.description}
        </Text>
      </View>
      
      {/* 滑块轨道 */}
      <View style={styles.sliderContainer}>
        <View style={styles.sliderTrack}>
          {/* 渐变背景 */}
          <Animated.View
            style={[
              styles.sliderFill,
              {
                width: slideAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                }),
                backgroundColor: mood.color,
              },
            ]}
          />
          
          {/* 滑块 */}
          <Animated.View
            {...panResponder.panHandlers}
            style={[
              styles.sliderThumb,
              {
                left: slideAnim.interpolate({
                  inputRange: [0, 100],
                  outputRange: [0, screenWidth - spacing.xl * 2 - 40],
                }),
                backgroundColor: mood.color,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <View style={styles.sliderThumbInner} />
          </Animated.View>
        </View>
        
        {/* 刻度标签 */}
        <View style={styles.sliderLabels}>
          <Text style={styles.sliderLabelText}>😔</Text>
          <Text style={styles.sliderLabelText}>😐</Text>
          <Text style={styles.sliderLabelText}>😊</Text>
        </View>
      </View>
      
      {/* 操作按钮 */}
      <View style={styles.actions}>
        <TouchableOpacity
          style={[styles.confirmButton, { backgroundColor: mood.color }]}
          onPress={() => onMoodSelect?.(mood)}
          activeOpacity={0.8}
        >
          <Text style={styles.confirmText}>就是这个感觉</Text>
        </TouchableOpacity>
        
        {showSkip && (
          <TouchableOpacity
            style={styles.skipButton}
            onPress={onSkip}
            activeOpacity={0.6}
          >
            <Text style={styles.skipText}>今天不想说</Text>
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );

  return isMinimal ? renderMinimalMode() : renderFullMode();
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxl,
    alignItems: 'center',
  },
  
  // 问候语
  prompt: {
    ...textStyles.title2,
    color: defaultTheme.text.primary,
    marginBottom: spacing.xl,
    textAlign: 'center',
  },
  
  // 情绪显示
  moodDisplay: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  
  moodEmoji: {
    fontSize: 48,
    marginBottom: spacing.sm,
  },
  
  moodLabel: {
    ...textStyles.title3,
    marginBottom: spacing.xs,
  },
  
  moodDescription: {
    ...textStyles.caption1,
    color: defaultTheme.text.secondary,
  },
  
  // 滑块
  sliderContainer: {
    width: '100%',
    marginBottom: spacing.xl,
  },
  
  sliderTrack: {
    height: 6,
    backgroundColor: defaultTheme.border,
    borderRadius: 3,
    marginBottom: spacing.md,
    overflow: 'visible',
  },
  
  sliderFill: {
    height: '100%',
    borderRadius: 3,
  },
  
  sliderThumb: {
    position: 'absolute',
    top: -17,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    ...shadows.md,
  },
  
  sliderThumbInner: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'white',
  },
  
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xs,
  },
  
  sliderLabelText: {
    fontSize: 14,
    opacity: 0.5,
  },
  
  // 操作按钮
  actions: {
    width: '100%',
    alignItems: 'center',
  },
  
  confirmButton: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: borderRadius.pill,
    marginBottom: spacing.md,
    ...shadows.sm,
  },
  
  confirmText: {
    ...textStyles.button,
    color: defaultTheme.text.primary,
  },
  
  skipButton: {
    paddingVertical: spacing.sm,
  },
  
  skipText: {
    ...textStyles.footnote,
    color: defaultTheme.text.secondary,
  },
  
  // 极简模式
  minimalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  
  minimalMood: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
    ...shadows.xs,
  },
  
  minimalEmoji: {
    fontSize: 20,
  },
  
  minimalSkip: {
    paddingHorizontal: spacing.sm,
  },
});

export default MoodCheckIn;